package com.stt.android.domain.advancedlaps

import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.infomodel.SummaryItem

fun LapsTableDataType.rowValue(row: LapsTableRow): Any? = when (this) {
    is LapsTableDataType.Summary -> when (summaryItem) {
        SummaryItem.DURATION -> row.duration
        SummaryItem.DISTANCE -> row.distance
        SummaryItem.AVGPACE -> row.avgSpeed
        SummaryItem.AVGHEARTRATE -> row.avgHR
        SummaryItem.MINHEARTRATE -> row.minHR
        SummaryItem.MAXHEARTRATE -> row.maxHR
        SummaryItem.ENERGY -> row.energy
        SummaryItem.RECOVERYTIME -> row.recoveryTime
        SummaryItem.AVGSPEED -> row.avgSpeed
        SummaryItem.AVGVERTICALSPEED -> row.avgVerticalSpeed
        SummaryItem.AVGCADENCE -> row.avgCadence
        SummaryItem.ASCENTALTITUDE -> row.ascent
        SummaryItem.DESCENTALTITUDE -> row.descent
        SummaryItem.HIGHALTITUDE -> row.maxAltitude
        SummaryItem.LOWALTITUDE -> row.minAltitude
        SummaryItem.AVGTEMPERATURE -> row.avgTemperature
        SummaryItem.MAXTEMPERATURE -> row.maxTemperature
        SummaryItem.AVGPOWER -> row.avgPower
        SummaryItem.MAXPOWER -> row.maxPower
        SummaryItem.AVGSWOLF -> row.avgSwolf
        SummaryItem.AVGSWIMSTROKERATE -> row.avgStrokeRate
        SummaryItem.AVGNAUTICALSPEED -> row.avgSpeed
        SummaryItem.MAXNAUTICALSPEED -> row.maxSpeed
        SummaryItem.NAUTICALDISTANCE -> row.distance
        SummaryItem.MAXPACE -> row.maxSpeed
        SummaryItem.MAXSPEED -> row.maxSpeed
        SummaryItem.ASCENTTIME -> row.ascentTime
        SummaryItem.DESCENTTIME -> row.descentTime
        SummaryItem.AVGSWIMPACE -> row.avgSpeed
        SummaryItem.CUMULATEDDISTANCE -> row.cumulatedDistance
        SummaryItem.CUMULATEDDURATION -> row.cumulatedDuration
        SummaryItem.SWIMDISTANCE -> row.distance
        SummaryItem.CUMULATEDSWIMDISTANCE -> row.cumulatedDistance
        SummaryItem.SWIMSTYLE -> row.swimStyle
        SummaryItem.MAXDEPTH -> row.maxDepth
        SummaryItem.AVGDEPTH -> row.avgDepth
        SummaryItem.MINDEPTH -> row.minDepth
        SummaryItem.DIVETIME -> row.diveTime
        SummaryItem.DIVETIMEMAX -> row.diveTimeMax
        SummaryItem.DIVERECOVERYTIME -> row.diveRecoveryTime
        SummaryItem.DIVEINWORKOUT -> row.diveInWorkout
        SummaryItem.REVOLUTIONCOUNT,
        SummaryItem.ROWINGSTROKECOUNT,
        SummaryItem.SKIPCOUNT -> row.repetitionCount
        SummaryItem.HRAEROBICTHRESHOLD -> row.aerobicHrThreshold
        SummaryItem.HRANAEROBICTHRESHOLD -> row.anaerobicHrThreshold
        SummaryItem.AEROBICPACETHRESHOLD -> row.aerobicPaceThreshold
        SummaryItem.ANAEROBICPACETHRESHOLD -> row.anaerobicPaceThreshold
        SummaryItem.AEROBICPOWERTHRESHOLD -> row.aerobicPowerThreshold
        SummaryItem.ANAEROBICPOWERTHRESHOLD -> row.anaerobicPowerThreshold
        SummaryItem.AEROBICDURATION -> row.aerobicDuration
        SummaryItem.ANAEROBICDURATION -> row.anaerobicDuration
        SummaryItem.VO2MAXDURATION -> row.vo2MaxDuration
        SummaryItem.BREASTSTROKEGLIDETIME -> row.breaststrokeGlideTime
        SummaryItem.BREATHINGRATE -> row.breathingRate
        SummaryItem.AVGBREASTSTROKEBREATHANGLE -> row.breaststrokeAvgBreathAngle
        SummaryItem.AVGFREESTYLEBREATHANGLE -> row.freestyleAvgBreathAngle
        // get the value according to swimming style
        SummaryItem.BREASTSTROKEHEADANGLE -> row.headAngle
        SummaryItem.FREESTYLEPITCHANGLE,
        SummaryItem.TOTALTIME,
        SummaryItem.PAUSETIME,
        SummaryItem.MOVINGTIME,
        SummaryItem.RESTTIME,
        SummaryItem.SWIMSTROKECOUNT,
        SummaryItem.SWIMSTROKEDISTANCE,
        SummaryItem.AVGPOWERWITHZERO,
        SummaryItem.PEAKPOWER30S,
        SummaryItem.PEAKPOWER1M,
        SummaryItem.PEAKPOWER3M,
        SummaryItem.PEAKPOWER5M,
        SummaryItem.MOVINGPACE,
        SummaryItem.PEAKPACE30S,
        SummaryItem.PEAKPACE1M,
        SummaryItem.PEAKPACE3M,
        SummaryItem.PEAKPACE5M,
        SummaryItem.MOVINGSPEED,
        SummaryItem.PEAKSPEED30S,
        SummaryItem.PEAKSPEED1M,
        SummaryItem.PEAKSPEED3M,
        SummaryItem.PEAKSPEED5M,
        SummaryItem.PERFORMANCELEVEL,
        SummaryItem.STEPS,
        SummaryItem.AVGSTEPCADENCE,
        SummaryItem.MAXSTEPCADENCE,
        SummaryItem.AVGSTRIDELENGTH,
        SummaryItem.PEAKVERTICALSPEED30S,
        SummaryItem.PEAKVERTICALSPEED1M,
        SummaryItem.PEAKVERTICALSPEED3M,
        SummaryItem.PEAKVERTICALSPEED5M,
        SummaryItem.PEAKEPOC,
        SummaryItem.FEELING,
        SummaryItem.MOVETYPE,
        SummaryItem.CATCHFISH,
        SummaryItem.CATCHBIGGAME,
        SummaryItem.CATCHSMALLGAME,
        SummaryItem.CATCHBIRD,
        SummaryItem.CATCHSHOTCOUNT,
        SummaryItem.AVGSEALEVELPRESSURE,
        SummaryItem.DIVEMODE,
        SummaryItem.DIVENUMBERINSERIES,
        SummaryItem.DIVEVISIBILITY,
        SummaryItem.DIVEMAXDEPTHTEMPERATURE,
        SummaryItem.SKIRUNCOUNT,
        SummaryItem.SKIDISTANCE,
        SummaryItem.SKITIME,
        SummaryItem.AVGSKISPEED,
        SummaryItem.MAXSKISPEED,
        SummaryItem.ESTVO2PEAK,
        SummaryItem.ALGORITHMLOCK,
        SummaryItem.DIVECNS,
        SummaryItem.DIVEOTU,
        SummaryItem.DIVEGASES,
        SummaryItem.PERSONAL,
        SummaryItem.GRADIENTFACTORS,
        SummaryItem.ALTITUDESETTING,
        SummaryItem.GASCONSUMPTION,
        SummaryItem.ALGORITHM,
        SummaryItem.TYPE,
        SummaryItem.PTE,
        SummaryItem.DIVEDISTANCE,
        SummaryItem.DIVEGASPRESSURE,
        SummaryItem.DIVESURFACETIME,
        SummaryItem.DIVEGASENDPRESSURE,
        SummaryItem.DIVEGASUSEDPRESSURE,
        SummaryItem.TRAININGSTRESSSCORE,
        SummaryItem.NORMALIZEDPOWER,
        SummaryItem.NORMALIZEDGRADEDPACE,
        SummaryItem.CO2EMISSIONSREDUCED,
        SummaryItem.DOWNHILLDESCENT,
        SummaryItem.DOWNHILLDURATION,
        SummaryItem.DOWNHILLCOUNT,
        SummaryItem.AVGDOWNHILLSPEED,
        SummaryItem.MAXDOWNHILLSPEED,
        SummaryItem.AVGDOWNHILLGRADE,
        SummaryItem.MAXDOWNHILLGRADE,
        SummaryItem.DOWNHILLMAXDESCENT,
        SummaryItem.DOWNHILLMAXLENGTH,
        SummaryItem.DOWNHILLDISTANCE,
        SummaryItem.MAXCADENCE,
        SummaryItem.FREESTYLEDURATION,
        SummaryItem.BREASTSTROKEDURATION ,
        SummaryItem.FREESTYLEPERCENT,
        SummaryItem.BREASTSTROKEPERCENT ,
        SummaryItem.MAXFREESTYLEBREATHANGLE,
        SummaryItem.MAXBREASTSTROKEBREATHANGLE,
        SummaryItem.FATCONSUMPTION,
        SummaryItem.CARBOHYDRATECONSUMPTION,
        SummaryItem.AVGGROUNDCONTACTTIME,
        SummaryItem.AVGVERTICALOSCILLATION,
        SummaryItem.AVGGROUNDCONTACTBALANCE,
        SummaryItem.CLIMBS,
        SummaryItem.CLIMBSCATEGORY1,
        SummaryItem.CLIMBSCATEGORY2,
        SummaryItem.CLIMBSCATEGORY3,
        SummaryItem.CLIMBSCATEGORY4,
        SummaryItem.CLIMBSCATEGORYHC,
        SummaryItem.CLIMBASCENTCATEGORY1,
        SummaryItem.CLIMBASCENTCATEGORY2,
        SummaryItem.CLIMBASCENTCATEGORY3,
        SummaryItem.CLIMBASCENTCATEGORY4,
        SummaryItem.CLIMBASCENTCATEGORYHC,
        SummaryItem.CLIMBDISTANCECATEGORY1,
        SummaryItem.CLIMBDISTANCECATEGORY2,
        SummaryItem.CLIMBDISTANCECATEGORY3,
        SummaryItem.CLIMBDISTANCECATEGORY4,
        SummaryItem.CLIMBDISTANCECATEGORYHC,
        SummaryItem.CLIMBDURATIONCATEGORY1,
        SummaryItem.CLIMBDURATIONCATEGORY2,
        SummaryItem.CLIMBDURATIONCATEGORY3,
        SummaryItem.CLIMBDURATIONCATEGORY4,
        SummaryItem.CLIMBDURATIONCATEGORYHC,
        SummaryItem.AVGASCENTSPEED,
        SummaryItem.AVGDESCENTSPEED,
        SummaryItem.MAXASCENTSPEED,
        SummaryItem.MAXDESCENTSPEED,
        SummaryItem.AVGDISTANCEPERSTROKE,
        SummaryItem.ZONESENSEBASELINE,
        SummaryItem.ZONESENSECUMULATIVEBASELINE,
        SummaryItem.AVGSKIPSRATE,
        SummaryItem.MAXAVGSKIPSRATE,
        SummaryItem.ROUNDS,
        SummaryItem.AVGSKIPSPERROUND,
        SummaryItem.MAXCONSECUTIVESKIPS,
        SummaryItem.AVGROWINGPACE,
        SummaryItem.AVGSTEPLENGTH,
        SummaryItem.ESTIMATEDFLOORSCLIMBED,
        SummaryItem.NONE -> null
    }
    is LapsTableDataType.SuuntoPlus -> row.suuntoPlusData?.get(suuntoPlusChannel)?.avg
}
