package com.stt.android.domain.workouts

import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummary
import com.stt.android.domain.workouts.stats.WorkoutStats
import com.stt.android.domain.workouts.summary.UserWorkoutSummaryByActivity
import com.stt.android.domain.workouts.summary.WorkoutFeeling
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.exceptions.InternalDataException
import kotlinx.coroutines.flow.Flow
import java.util.EnumSet

const val DEFAULT_PUBLIC_WORKOUTS_FETCH_LIMIT = 50

enum class AdditionalData(val value: String) {
    PHOTOS("photos"),
    VIDEOS("videos"),
    COMMENTS("comments"),
    USER_REACTED("user_reacted"),
    ACHIEVEMENTS("achievements")
}

enum class Extension(val value: String) {
    DIVE("DiveHeaderExtension"),
    FITNESS("FitnessExtension"),
    INTENSITY("IntensityExtension"),
    SKI("SkiExtension"),
    SUMMARY("SummaryExtension"),
    SWIMMING("SwimmingHeaderExtension"),
    WEATHER("WeatherExtension")
}

// todo Really hard to say which operates locally and which remote, or both
interface WorkoutDataSource {
    suspend fun fetchWorkout(
        workoutKey: String,
    ): DomainWorkout

    suspend fun fetchCombinedWorkout(
        username: String,
        workoutKey: String,
        extensions: EnumSet<Extension>?,
        additionalData: EnumSet<AdditionalData>?
    ): DomainWorkout

    suspend fun store(workoutHeader: WorkoutHeader): WorkoutHeader

    suspend fun fetchWorkoutHeader(id: Int): Flow<WorkoutHeader?>
    suspend fun fetchFolloweesWorkoutsPaged(): Flow<List<DomainWorkout>>
    suspend fun fetchOwnWorkoutsPaged(): Flow<List<DomainWorkout>>

    suspend fun fetchPublicWorkouts(
        lowerlat: Double,
        lowerlng: Double,
        upperlat: Double,
        upperlng: Double,
        limit: Int
    ): List<Pair<User, DomainWorkout>>

    suspend fun fetchWorkoutStatsForUser(username: String): WorkoutStats

    suspend fun uploadLocalWorkoutData(): List<Result<Pair<Any, String>>>

    fun clearOwnWorkoutsFetchedTimestamp()

    fun clearFolloweesWorkoutsFetchedTimestamp()

    suspend fun clearOldFolloweesWorkoutsAboveMaxCount(currentUser: User)

    suspend fun clearOldFolloweesWorkouts(currentUser: User)

    fun getAllCommuteWorkouts(
        username: String,
        minimumStartTime: Long?,
        maximumStartTime: Long?
    ): Flow<List<WorkoutHeader>>

    suspend fun fetchUserWorkoutSummaryByActivity(
        username: String,
        minStartTime: Long,
        maxStartTime: Long
    ): Map<Int, UserWorkoutSummaryByActivity>

    suspend fun fetchUserWorkoutFeelings(
        username: String,
        minStartTime: Long,
        maxStartTime: Long
    ): List<WorkoutFeeling>

    suspend fun fetchUserWorkoutsBasic(
        username: String,
        minStartTime: Long,
        maxStartTime: Long
    ): List<BasicWorkoutHeader>

    @Throws(InternalDataException::class)
    fun loadLatestWorkoutsOfActivityTypes(
        username: String,
        activityTypes: List<Int>?,
        maxCount: Int,
        minDurationSeconds: Long,
        fromTimestampMillis: Long
    ): List<WorkoutHeader>

    fun getAllWorkoutsForSummary(
        username: String,
        activityIds: List<Int>?,
        minStartTimeInclusive: Long?,
        maxStartTimeInclusive: Long?,
        minTotalDistanceInclusive: Double?,
        maxTotalDistanceInclusive: Double?,
        suuntoTags: List<SuuntoTag>?,
        userTags: List<UserTag>?
    ): Flow<List<SummaryWorkoutHeader>>

    suspend fun getAllWorkoutsForZoneSenseSync(username: String): List<ZoneSenseSyncWorkoutHeader>

    suspend fun getWorkoutsForZoneSense(
        username: String,
        activityTypeIds: Set<Int>,
        untilInMillsSinceEpoch: Long,
    ): List<ZoneSenseSyncWorkoutHeader>

    fun hasWorkouts(username: String): Flow<Boolean>

    suspend fun loadAllDoneActivityTypes(username: String): List<Int>

    suspend fun loadRecentActivityTypesIds(username: String, limit: Int): List<Int>

    suspend fun updateSharingFlagsByUsername(username: String, sharingFlags: Int)

    suspend fun fetchCompetitionWorkoutResult(username: String, workoutKey: String): CompetitionWorkoutSummary
}
