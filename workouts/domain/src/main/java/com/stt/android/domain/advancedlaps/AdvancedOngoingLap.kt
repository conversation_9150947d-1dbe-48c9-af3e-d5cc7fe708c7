package com.stt.android.domain.advancedlaps

import com.stt.android.core.domain.SuuntoPlusChannel
import com.stt.android.domain.sml.SmlTimedStreamSamplePoint
import com.stt.android.domain.workouts.AvgMinMax
import java.time.Duration
import kotlin.math.abs

class AdvancedOngoingLap(
    val workoutDurationOnStart: Long,
    val workoutDistanceOnStart: Float,
    val ascentDescentThreshold: Float // threshold in meters for ascent/descent calculation
) {

    var duration: Long = 0
    var distance: Float = 0.0f
    var cumulativeDuration: Long = 0
    var cumulativeDistance: Float = 0.0f
    var lastRegisteredAscentTime: Long = 0L
    var heartRateStatistics = Statistics()
    var speedStatistics = Statistics()
    var altitudeStatistics = Statistics()
    var ascentDescentStatistics = Statistics()
    var temperatureStatistics: Statistics? = null
    var verticalSpeedStatistics = Statistics()
    var powerStatistics = Statistics()
    var cadenceStatistics = Statistics()
    var repetitionCount: Int = 0
    var strideStatistics = Statistics()
    var fatConsumption: Int = 0
    var carbohydrateConsumption: Int = 0
    var groundContactTimeStatistics = Statistics()
    var verticalOscillationStatistics = Statistics()
    var leftGroundContactBalanceStatistics = Statistics()
    var rightGroundContactBalanceStatistics = Statistics()
    var ascentSpeed = Statistics()
    var descentSpeed = Statistics()
    var distancePerStroke = Statistics()
    var breatheRateStatistics: Statistics? = null
    var breaststrokeGlideTimeStatistics: Statistics? = null
    var avgBreaststrokeBreathAngleStatistics: Statistics? = null
    var avgFreestyleBreathAngleStatistics: Statistics? = null
    var freestyleHeadAngleStatistics: Statistics? = null
    var breaststrokeHeadAngleStatistics: Statistics? = null

    // todo improve by using better class than Statistics for calculating averages
    val suuntoPlusSamplesStatistics = mutableMapOf<SuuntoPlusChannel, Statistics>()
    private var previousLapCumulativeDistance: Float = 0.0f
    private var previousLapCumulativeDuration: Long = 0
    private var workoutDistanceOnEnd: Float = 0.0f

    fun lapFinished() {
        this.previousLapCumulativeDistance = this.cumulativeDistance
        this.previousLapCumulativeDuration = this.cumulativeDuration
        this.distance = 0.0f
        this.duration = 0
        this.speedStatistics = Statistics()
        this.altitudeStatistics = Statistics()
        // carry over ascent/descent ref altitude to next lap
        val lastAscentDescentAltitude = this.ascentDescentStatistics.lastValue
        this.ascentDescentStatistics = Statistics().apply {
            addSample(lastAscentDescentAltitude)
        }
        this.temperatureStatistics = null
        this.verticalSpeedStatistics = Statistics()
        this.powerStatistics = Statistics()
        this.heartRateStatistics = Statistics()
        this.cadenceStatistics = Statistics()
        this.repetitionCount = 0
        this.suuntoPlusSamplesStatistics.clear()
        this.strideStatistics = Statistics()
        this.fatConsumption = 0
        this.carbohydrateConsumption = 0
        this.groundContactTimeStatistics = Statistics()
        this.verticalOscillationStatistics = Statistics()
        this.leftGroundContactBalanceStatistics = Statistics()
        this.rightGroundContactBalanceStatistics = Statistics()
        this.ascentSpeed = Statistics()
        this.descentSpeed = Statistics()
        this.distancePerStroke = Statistics()
        this.breatheRateStatistics = null
        this.breaststrokeGlideTimeStatistics = null
        this.avgBreaststrokeBreathAngleStatistics = null
        this.avgFreestyleBreathAngleStatistics = null
        this.freestyleHeadAngleStatistics = null
        this.breaststrokeHeadAngleStatistics = null
    }

    fun updateOngoingLap(sample: SmlTimedStreamSamplePoint) {
        if (sample.samplePoint.heartrate != null) {
            updateHeartRate(sample.samplePoint.heartrate)
        }

        if (!sample.samplePoint.isHeartRateOnly()) {
            updateDuration(sample.time)
            updateCumulativeDuration(sample.time)
            sample.samplePoint.cumulativeDistance?.run {
                updateDistance(this)
                updateCumulativeDistance(this)
            }
            sample.samplePoint.speed?.run { updateSpeed(this) }
            sample.samplePoint.altitude?.run { updateAltitude(sample.time, this) }
            sample.samplePoint.temperature?.run { updateTemperature(this) }
            sample.samplePoint.verticalSpeed?.run { updateVerticalSpeed(this) }
            sample.samplePoint.power?.run { updatePower(this) }
            sample.samplePoint.cadence?.run { updateCadence(this) }
            sample.samplePoint.suuntoPlusSample?.also { (channel, value) ->
                updateSuuntoPlusSample(channel, value)
            }
            sample.samplePoint.breaststrokeHeadAngle?.run { updateBreaststrokeHeadAngle(this) }
            sample.samplePoint.breaststrokeGlideTime?.run { updateBreaststrokeGlideTime(this) }
            sample.samplePoint.breathingRate?.run { updateBreatheRate(this) }
            sample.samplePoint.freestyleHeadAngle?.run { updateFreestyleHeadAngle(this) }
            sample.samplePoint.breaststrokeAvgBreathAngle?.run { updateAvgBreaststrokeBreathAngle(this) }
            sample.samplePoint.freestyleAvgBreathAngle?.run { updateAvgFreestyleBreathAngle(this) }
        }
    }

    fun getSuuntoPlusData(): Map<SuuntoPlusChannel, AvgMinMax>? =
        suuntoPlusSamplesStatistics.mapValues { (_, stats) ->
            AvgMinMax(stats.avg, stats.min, stats.max)
        }.takeIf { it.isNotEmpty() }

    private fun updateSuuntoPlusSample(channel: SuuntoPlusChannel, value: Float) {
        suuntoPlusSamplesStatistics.getOrPut(channel) { Statistics() }.addSample(value.toDouble())
    }

    private fun updateDuration(currentWorkoutDuration: Long) {
        this.duration = currentWorkoutDuration - this.previousLapCumulativeDuration
    }

    private fun updateCumulativeDuration(currentWorkoutDuration: Long) {
        this.cumulativeDuration = currentWorkoutDuration
    }

    private fun updateDistance(currentWorkoutDistance: Float) {
        this.distance = currentWorkoutDistance - this.previousLapCumulativeDistance
    }

    private fun updateCumulativeDistance(currentWorkoutDistance: Float) {
        this.workoutDistanceOnEnd = currentWorkoutDistance
        if (currentWorkoutDistance != 0.0f) {
            this.cumulativeDistance = currentWorkoutDistance
        }
    }

    private fun updateSpeed(currentWorkoutSpeed: Float) {
        speedStatistics.addSample(currentWorkoutSpeed.toDouble())
    }

    private fun updateAltitude(time: Long, currentAltitude: Float) {
        altitudeStatistics.addSample(currentAltitude.toDouble())
        // ascent/descent logic taken from NG algorithm
        val isFirstAscentDescentSample = lastRegisteredAscentTime == 0L

        val isLastAscentDescentSampleTooLongAgo = !isFirstAscentDescentSample &&
            (time - lastRegisteredAscentTime > ascentDescentTimeThreshold)

        val isLastAscentDescentSampleOverThreshold =
            abs(ascentDescentStatistics.lastValue - currentAltitude) >= ascentDescentThreshold

        val shouldRegisterAscentDescent = isFirstAscentDescentSample || isLastAscentDescentSampleOverThreshold

        when {
            isLastAscentDescentSampleTooLongAgo -> {
                lastRegisteredAscentTime = time
                ascentDescentStatistics.lastValue = currentAltitude.toDouble()
            }
            shouldRegisterAscentDescent -> {
                lastRegisteredAscentTime = time
                ascentDescentStatistics.addSample(currentAltitude.toDouble())
            }
            else -> {
                // in this case we didn't make the threshold, so we re-add the last registered value
                ascentDescentStatistics.addSample(ascentDescentStatistics.lastValue)
            }
        }
    }

    private fun updateTemperature(currentTemperature: Float) {
        if (temperatureStatistics == null) temperatureStatistics = Statistics()
        temperatureStatistics?.addSample(currentTemperature.toDouble())
    }

    private fun updateVerticalSpeed(currentVerticalSpeed: Float) {
        verticalSpeedStatistics.addSample(currentVerticalSpeed.toDouble())
    }

    private fun updatePower(currentPower: Float) {
        powerStatistics.addSample(currentPower.toDouble())
    }

    private fun updateHeartRate(currentHeartRate: Float) {
        heartRateStatistics.addSample(currentHeartRate.toDouble())
    }

    private fun updateCadence(currentCadence: Float) {
        cadenceStatistics.addSample(currentCadence.toDouble())
    }

    private fun updateBreatheRate(breatheRate: Int) {
        if (breatheRateStatistics == null) breatheRateStatistics = Statistics()
        breatheRateStatistics?.addSample(breatheRate.toDouble())
    }

    private fun updateBreaststrokeGlideTime(breaststrokeGlideTime: Int) {
        if (breaststrokeGlideTimeStatistics == null) breaststrokeGlideTimeStatistics = Statistics()
        breaststrokeGlideTimeStatistics?.addSample(breaststrokeGlideTime.toDouble())
    }

    private fun updateAvgBreaststrokeBreathAngle(avgBreaststrokeBreathAngle: Int) {
        if (avgBreaststrokeBreathAngleStatistics == null) avgBreaststrokeBreathAngleStatistics =
            Statistics()
        avgBreaststrokeBreathAngleStatistics?.addSample(avgBreaststrokeBreathAngle.toDouble())
    }

    private fun updateAvgFreestyleBreathAngle(avgFreestyleBreathAngle: Int) {
        if (avgFreestyleBreathAngleStatistics == null) avgFreestyleBreathAngleStatistics =
            Statistics()
        avgFreestyleBreathAngleStatistics?.addSample(avgFreestyleBreathAngle.toDouble())
    }

    private fun updateFreestyleHeadAngle(freestyleHeadAngle: Int) {
        if (freestyleHeadAngleStatistics == null) freestyleHeadAngleStatistics = Statistics()
        if (freestyleHeadAngle > 0)
            freestyleHeadAngleStatistics?.addSample(freestyleHeadAngle.toDouble())
    }

    private fun updateBreaststrokeHeadAngle(breaststrokeHeadAngle: Int) {
        if (breaststrokeHeadAngleStatistics == null) breaststrokeHeadAngleStatistics = Statistics()
        if (breaststrokeHeadAngle > 0)
            breaststrokeHeadAngleStatistics?.addSample(breaststrokeHeadAngle.toDouble())
    }

    companion object {
        private val ascentDescentTimeThreshold: Long = Duration.ofMinutes(3).toMillis()
    }
}
