package com.stt.android.domain.workouts

import android.content.res.Resources
import android.os.Parcelable
import com.stt.android.domain.Filterable
import com.stt.android.domain.Point
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.SharingOption
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS
import com.stt.android.domain.workouts.zonesense.ZoneSense
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.utils.CoordinateUtils
import com.stt.android.workouts.domain.R
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Parcelize
data class WorkoutHeader(
    /**
     * If the workout is local the ID is based on hashed {@link #startTime}. If the workout comes
     * from the backend we use a hash based on the {@link #key}. The hash is used to simplify
     * updates to a workout coming from the backend. We are able to update the workout in the
     * database directly (based on the hashed ID).
     */
    val id: Int,
    /**
     * Database unique {@code Key}. Null if the workout is only local (i.e. has not yet been synced)
     */
    val key: String?,
    val totalDistance: Double,
    val maxSpeed: Double,
    /**
     * Value of {@link ActivityType#getId()}
     */
    val activityTypeId: Int,
    val avgSpeed: Double,
    val description: String?,
    val startPosition: Point?,
    val stopPosition: Point?,
    val centerPosition: Point?,
    val startTime: Long, // ms UTC
    val stopTime: Long, // ms UTC
    val totalTime: Double, // seconds
    val energyConsumption: Double, // kCal
    val username: String,
    val heartRateAverage: Double, // bpm
    /**
     * The percentage of the average heart rate value for this workout compared
     * to the user set max heart rate value. For example 78% means that the
     * heart rate average was 78% of the maximum.
     */
    val heartRateAvgPercentage: Double, // 0..100
    val heartRateMax: Double, // bpm
    /**
     * The percentage of the maximum heart rate value for this workout compared
     * to the user set max heart rate value. For example 50% means that the
     * maximum value reached was half of that set by the user as maximum.
     */
    val heartRateMaxPercentage: Double, // 0..100
    val heartRateUserSetMax: Double, // bpm
    val averageCadence: Int,
    val maxCadence: Int,
    val pictureCount: Int,
    val viewCount: Int,
    val commentCount: Int,
    val sharingFlags: Int,
    val stepCount: Int,
    val polyline: String?,
    val manuallyAdded: Boolean,
    val reactionCount: Int,
    val totalAscent: Double, // meters
    val totalDescent: Double, // meters
    val recoveryTime: Long, // seconds
    val locallyChanged: Boolean = false,
    val deleted: Boolean = false,
    val seen: Boolean = false,
    val maxAltitude: Double?, // decimeters !!!
    val minAltitude: Double?, // decimeters !!!
    val extensionsFetched: Boolean = false,
    val tss: TSS? = null,
    /**
     * List of all TSS values that have been calculated for the workout.
     * The TSS value in use is stored in tss field, and in case of it being manually set
     * then it might not be part of the tssList.
     * Null and empty list should be handled as being equal, and the order doesn't matter.
     * A List is used instead of Set to for consistency with the name from backend and possible
     * future uses for the order
     */
    val tssList: List<TSS> = emptyList(),
    val suuntoTags: List<SuuntoTag>,
    val userTags: List<UserTag>,
    val zoneSense: ZoneSense? = null,
    var estimatedFloorsClimbed: Int? = null
) : Parcelable, Searchable, Filterable {

    @IgnoredOnParcel
    val filename = "workout_$id"

    @IgnoredOnParcel
    val isPolylineEmpty = polyline.isNullOrBlank()

    @IgnoredOnParcel
    val activityType = ActivityType.valueOf(activityTypeId)

    @IgnoredOnParcel
    val polylineHashCode = (polyline ?: "").hashCode()

    /**
     * @return an unmodifiable list which contains all the current
     * SharingOptions for this WorkoutHeader
     */
    val sharingOptions: List<SharingOption>
        get() = SharingOption.valuesOf(sharingFlags)

    /**
     * true if workout is shared for followers or public
     */
    @IgnoredOnParcel
    val isShared: Boolean = sharingFlags and SharingOption.SHARED_MASK != 0

    /**
     * true if workout is shared for public
     */
    @IgnoredOnParcel
    val isPublic: Boolean = sharingFlags and SharingOption.PUBLIC_MASK != 0

    @IgnoredOnParcel
    val isSynced: Boolean = key != null

    @IgnoredOnParcel
    val avgSpeedInKmPerHour: Double = avgSpeed * 3.6

    @IgnoredOnParcel
    val co2EmissionsReduced: Double = totalDistance * 0.170652 / 1000

    @IgnoredOnParcel
    val isCommute: Boolean = suuntoTags.contains(SuuntoTag.COMMUTE)

    override fun getSearchString(resources: Resources): String {
        val sb = StringBuilder()
        if (description != null) {
            // We use the device locale to lower case the description
            sb.append(description.lowercase())
            sb.append(' ')
        }

        suuntoTags.forEach {
            sb.append(it.getSearchString(resources))
            sb.append(' ')
        }

        for (userTag in userTags) {
            sb.append(userTag.name.lowercase())
            sb.append(' ')
        }

        // We use the strings' locale to lower case the activity type and month
        val stringsLocale = Locale(resources.getString(R.string.language_code))
        sb.append(activityType.getSearchString(resources))
        sb.append(' ')
        val df = SimpleDateFormat("MMMM yyyy", stringsLocale)
        sb.append(df.format(Date(startTime)).lowercase(stringsLocale))
        return sb.toString()
    }

    override fun applyFilter(
        constraints: Array<out CharSequence>?,
        resources: Resources
    ): Boolean {
        if (constraints.isNullOrEmpty()) {
            return true
        }
        val valueText = getSearchString(resources)

        var allTermsMatch = true
        // Quite often we'll get one term only so let's optimize for it
        if (constraints.size == 1) {
            if (!valueText.contains(constraints[0])) {
                allTermsMatch = false
            }
        } else {
            // Check if there's some term that is not in the workout
            for (searchTerm in constraints) {
                if (!valueText.contains(searchTerm)) {
                    allTermsMatch = false
                    break
                }
            }
        }
        return allTermsMatch
    }

    fun toBuilder() = WorkoutHeaderBuilder(this)

    /**
     * Convenient method to check if every value of this instance is the same as {@code other}
     * instance
     * without taking into account {@link #locallyChanged} field.
     *
     * @return true if all fields are the same (regardless of {@link #locallyChanged} value)
     */
    fun equalsExceptLocallyChanged(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || javaClass != other.javaClass) return false
        return this == (other as WorkoutHeader).copy(locallyChanged = this.locallyChanged)
    }

    fun hasDescription(): Boolean {
        return !description.isNullOrBlank()
    }

    fun hasTags(): Boolean = userTags.isNotEmpty() || suuntoTags.isNotEmpty()

    /**
     * DistanceFromStartToEnd is meant to capture how far the starting point and the ending point of the workout are from each other.
     * So if you go for a 10km run, but start and end in your front door, DistanceInMeters would be 10000, but DistanceFromStartToEnd 0
     * @return return the distance between two positions in meter, or null if start and/or end is null
     */
    fun calculateDistanceFromStartToEndOrNull(): Double? {
        val startLatitude = startPosition?.latitude
        val startLongitude = startPosition?.longitude
        val stopLatitude = stopPosition?.latitude
        val stopLongitude = stopPosition?.longitude

        if (startLatitude == null || startLongitude == null || stopLatitude == null || stopLongitude == null) return null
        return CoordinateUtils.distanceBetween(
            startLatitude,
            startLongitude,
            stopLatitude,
            stopLongitude
        )
    }

    companion object {
        const val LOCAL_WORKOUT_PREFIX = "local_"
        const val REMOTE_WORKOUT_PREFIX = "remote_"

        fun generateIdFromRemote(workoutKey: String): Int =
            (REMOTE_WORKOUT_PREFIX + workoutKey).hashCode()

        @JvmStatic
        fun builder() = WorkoutHeaderBuilder()

        // todo remove when not needed anymore
        @JvmStatic
        fun local(
            totalDistance: Double,
            maxSpeed: Double,
            activityType: ActivityType,
            avgSpeed: Double,
            description: String?,
            startPosition: Point?,
            stopPosition: Point?,
            centerPosition: Point?,
            startTime: Long,
            stopTime: Long,
            totalTime: Double,
            energyConsumption: Double,
            username: String,
            heartRateAvg: Double,
            heartRateAvgPercentage: Double,
            heartRateMax: Double,
            heartRateMaxPercentage: Double,
            heartRateUserSetMax: Double,
            averageCadence: Int,
            maxCadence: Int,
            pictureCount: Int,
            viewCount: Int,
            commentCount: Int,
            sharingFlags: Int,
            stepCount: Int,
            manuallyCreated: Boolean,
            simplifiedPolyline: String?,
            reactionCount: Int,
            totalAscent: Double,
            totalDescent: Double,
            recoveryTime: Long,
            maxAltitude: Double,
            minAltitude: Double,
            tss: TSS?,
            tssList: List<TSS>?,
            estimatedFloorsClimbed: Int
        ): WorkoutHeader {
            return builder()
                .id((LOCAL_WORKOUT_PREFIX + startTime).hashCode())
                .key(null)
                .totalDistance(totalDistance)
                .maxSpeed(maxSpeed)
                .activityId(activityType.id)
                .avgSpeed(avgSpeed)
                .description(description)
                .startPosition(startPosition)
                .stopPosition(stopPosition)
                .centerPosition(centerPosition)
                .startTime(startTime, false)
                .stopTime(stopTime)
                .totalTime(totalTime, false)
                .energyConsumption(energyConsumption)
                .userName(username)
                .heartRateAvg(heartRateAvg)
                .heartRateAvgPercentage(heartRateAvgPercentage)
                .heartRateMax(heartRateMax)
                .heartRateMaxPercentage(heartRateMaxPercentage)
                .heartRateUserSetMax(heartRateUserSetMax)
                .averageCadence(averageCadence)
                .maxCadence(maxCadence)
                .pictureCount(pictureCount)
                .viewCount(viewCount)
                .commentCount(commentCount)
                .sharingFlags(sharingFlags)
                .polyline(simplifiedPolyline)
                .stepCount(stepCount)
                .reactionCount(reactionCount)
                .deleted(false)
                .manuallyCreated(manuallyCreated)
                .locallyChanged(false)
                .totalAscent(totalAscent)
                .totalDescent(totalDescent)
                .seen(true)
                .recoveryTime(recoveryTime)
                .maxAltitude(maxAltitude)
                .minAltitude(minAltitude)
                .extensionsFetched(true)
                .tss(tss)
                .tssList(tssList ?: listOf())
                .estimatedFloorsClimbed(estimatedFloorsClimbed)
                .build()
        }

        /**
         * Use this "constructor" when the workout has been created by the user manually.
         * todo remove when not needed anymore
         */
        @JvmStatic
        fun manual(
            totalDistance: Double,
            maxSpeed: Double,
            activityType: ActivityType,
            avgSpeed: Double,
            description: String?,
            startPosition: Point?,
            stopPosition: Point?,
            centerPosition: Point?,
            startTime: Long,
            stopTime: Long,
            totalTime: Double,
            energyConsumption: Double,
            username: String,
            heartRateAvg: Double,
            heartRateAvgPercentage: Double,
            heartRateMax: Double,
            heartRateMaxPercentage: Double,
            heartRateUserSetMax: Double,
            averageCadence: Int,
            maxCadence: Int,
            pictureCount: Int,
            viewCount: Int,
            commentCount: Int,
            sharingFlags: Int,
            stepCount: Int,
            reactionCount: Int,
            totalAscent: Double,
            totalDescent: Double,
            recoveryTime: Long,
            maxAltitude: Double,
            minAltitude: Double,
            tss: TSS?,
            tssList: List<TSS>?,
            estimatedFloorsClimbed: Int
        ): WorkoutHeader {
            return builder()
                .id((LOCAL_WORKOUT_PREFIX + startTime).hashCode())
                .key(null)
                .totalDistance(totalDistance)
                .maxSpeed(maxSpeed)
                .activityId(activityType.id)
                .avgSpeed(avgSpeed)
                .description(description)
                .startPosition(startPosition)
                .stopPosition(stopPosition)
                .centerPosition(centerPosition)
                .startTime(startTime, false)
                .stopTime(stopTime)
                .totalTime(totalTime, false)
                .energyConsumption(energyConsumption)
                .userName(username)
                .heartRateAvg(heartRateAvg)
                .heartRateAvgPercentage(heartRateAvgPercentage)
                .heartRateMax(heartRateMax)
                .heartRateMaxPercentage(heartRateMaxPercentage)
                .heartRateUserSetMax(heartRateUserSetMax)
                .averageCadence(averageCadence)
                .maxCadence(maxCadence)
                .pictureCount(pictureCount)
                .viewCount(viewCount)
                .commentCount(commentCount)
                .sharingFlags(sharingFlags)
                .polyline(null)
                .stepCount(stepCount)
                .reactionCount(reactionCount)
                .deleted(false)
                .manuallyCreated(true)
                .totalAscent(totalAscent)
                .totalDescent(totalDescent)
                .locallyChanged(false)
                .seen(true)
                .recoveryTime(recoveryTime)
                .maxAltitude(maxAltitude)
                .minAltitude(minAltitude)
                .extensionsFetched(true)
                .tss(tss)
                .tssList(tssList ?: listOf())
                .estimatedFloorsClimbed(estimatedFloorsClimbed)
                .build()
        }

        // todo remove when not needed anymore
        @JvmStatic
        fun remote(
            key: String,
            totalDistance: Double,
            maxSpeed: Double,
            activityType: ActivityType,
            avgSpeed: Double,
            description: String?,
            startPosition: Point?,
            stopPosition: Point?,
            centerPosition: Point?,
            startTime: Long,
            stopTime: Long,
            totalTime: Double,
            energyConsumption: Double,
            username: String,
            heartRateAvg: Double,
            heartRateAvgPercentage: Double,
            heartRateMax: Double,
            heartRateMaxPercentage: Double,
            heartRateUserSetMax: Double,
            averageCadence: Int,
            maxCadence: Int,
            pictureCount: Int,
            viewCount: Int,
            commentCount: Int,
            sharingFlags: Int,
            stepCount: Int,
            manuallyCreated: Boolean,
            polyline: String?,
            reactionCount: Int,
            totalAscent: Double,
            totalDescent: Double,
            recoveryTime: Long,
            maxAltitude: Double,
            minAltitude: Double,
            tss: TSS?,
            tssList: List<TSS>?,
            suuntoTags: List<SuuntoTag>?,
            estimatedFloorsClimbed: Int
        ): WorkoutHeader = builder()
            .id(generateIdFromRemote(key))
            .key(key)
            .totalDistance(totalDistance)
            .maxSpeed(maxSpeed)
            .activityId(activityType.id)
            .avgSpeed(avgSpeed)
            .description(description)
            .startPosition(startPosition)
            .stopPosition(stopPosition)
            .centerPosition(centerPosition)
            .startTime(startTime, false)
            .stopTime(stopTime)
            .totalTime(totalTime, false)
            .energyConsumption(energyConsumption)
            .userName(username)
            .heartRateAvg(heartRateAvg)
            .heartRateAvgPercentage(heartRateAvgPercentage)
            .heartRateMax(heartRateMax)
            .heartRateMaxPercentage(heartRateMaxPercentage)
            .heartRateUserSetMax(heartRateUserSetMax)
            .averageCadence(averageCadence)
            .maxCadence(maxCadence)
            .pictureCount(pictureCount)
            .viewCount(viewCount)
            .commentCount(commentCount)
            .sharingFlags(sharingFlags)
            .polyline(polyline)
            .stepCount(stepCount)
            .reactionCount(reactionCount)
            .deleted(false)
            .manuallyCreated(manuallyCreated)
            .totalAscent(totalAscent)
            .totalDescent(totalDescent)
            .locallyChanged(false)
            .seen(true)
            .recoveryTime(recoveryTime)
            .maxAltitude(maxAltitude)
            .minAltitude(minAltitude)
            .tss(tss)
            .tssList(tssList ?: listOf())
            .suuntoTags(suuntoTags ?: listOf())
            .estimatedFloorsClimbed(estimatedFloorsClimbed)
            .build()
    }
}

val WorkoutHeader.isMultisport: Boolean
    get() = listOf(
        ActivityMapping.TRIATHLON.stId,
        ActivityMapping.DUATHLON.stId,
        ActivityMapping.SWIMRUN.stId,
        ActivityMapping.AQUATHLON.stId,
        ActivityMapping.MULTISPORT.stId
    ).contains(activityTypeId)

val WorkoutHeader.isHuntingOrFishing: Boolean
    get() = listOf(
        ActivityMapping.FISHING.stId,
        ActivityMapping.HUNTING.stId,
    ).contains(activityTypeId)

val WorkoutHeader.isSki: Boolean
    get() = listOf(
        ActivityMapping.DOWNHILLSKIING.stId,
        ActivityMapping.SNOWBOARDING.stId,
        ActivityMapping.TELEMARKSKIING.stId,
    ).contains(activityTypeId)

val WorkoutHeader.isDiving: Boolean
    get() = listOf(
        ActivityMapping.SCUBADIVING.stId,
        ActivityMapping.FREEDIVING.stId,
    ).contains(activityTypeId)

fun WorkoutHeader.hasValidStartAndStopPositions(): Boolean =
    startPosition?.isOrigin == false && stopPosition?.isOrigin == false

fun WorkoutHeader.getSingleValidLocation(): Point? = listOfNotNull(stopPosition, startPosition)
    .filter { !it.isOrigin }
    .distinct()
    .takeIf { it.size == 1 }
    ?.firstOrNull()

@IgnoredOnParcel
val WorkoutHeader.isPrivate: Boolean
    get() = (sharingFlags and SharingOption.FOLLOWERS.backendId) == 0 &&
        (sharingFlags and SharingOption.EVERYONE.backendId) == 0 &&
        (sharingFlags and SharingOption.FACEBOOK.backendId) == 0 &&
        (sharingFlags and SharingOption.TWITTER.backendId) == 0
