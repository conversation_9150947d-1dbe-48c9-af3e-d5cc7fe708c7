package com.suunto.headset.ui.ows

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.suunto.headset.R
import com.suunto.headset.ai.VoiceState
import com.suunto.headset.model.AIChatMessage
import com.suunto.headset.model.AIChatSender
import com.suunto.headset.model.VoiceInputInfo
import com.suunto.headset.ui.components.CommonTopAppBar
import com.suunto.headset.ui.components.LottieAnimationView
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Composable
fun AIChatScreen(
    messages: ImmutableList<AIChatMessage>,
    onBackClick: () -> Unit,
    voiceInputInfo: VoiceInputInfo,
    onVoiceInputAction: () -> Unit,
    modifier: Modifier = Modifier
) {
    val listState = rememberLazyListState()
    LaunchedEffect(messages.size, voiceInputInfo) {
        if (messages.isNotEmpty()) {
            listState.animateScrollToItem(messages.size - 1)
        }
        if (voiceInputInfo.state == VoiceState.PROGRESS) {
            listState.animateScrollToItem(messages.size)
        }
    }
    Scaffold(
        modifier = modifier,
        topBar = {
            CommonTopAppBar(
                title = stringResource(id = R.string.ai_chat_title),
                onBackClick = onBackClick
            )
        },
        containerColor = MaterialTheme.colorScheme.surface
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface)
        ) {
            LazyColumn(
                state = listState,
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                reverseLayout = false,
                contentPadding = PaddingValues(
                    vertical = MaterialTheme.spacing.medium,
                    horizontal = MaterialTheme.spacing.medium
                )
            ) {
                items(messages, key = { it.id }) { msg ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = MaterialTheme.spacing.xsmall),
                        horizontalArrangement = if (msg.sender == AIChatSender.USER) Arrangement.End else Arrangement.Start
                    ) {
                        ChatBubble(
                            text = msg.text,
                            isUser = msg.sender == AIChatSender.USER
                        )
                    }
                }
                if (voiceInputInfo.state == VoiceState.PROGRESS) {
                    item {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = MaterialTheme.spacing.xsmall),
                            horizontalArrangement = if (voiceInputInfo.fromUser) Arrangement.End else Arrangement.Start
                        ) {
                            ChatBubble("", isUser = voiceInputInfo.fromUser)
                        }
                    }
                }
            }
            VoiceInputBar(
                voiceInputInfo = voiceInputInfo,
                onAction = onVoiceInputAction,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun ChatBubble(text: String, isUser: Boolean, modifier: Modifier = Modifier) {
    val bubbleColor =
        if (isUser) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.nearWhite
    val textColor =
        if (isUser) MaterialTheme.colorScheme.surface else MaterialTheme.colorScheme.onSurface
    Box(
        modifier = modifier
            .background(
                color = bubbleColor,
                shape = RoundedCornerShape(20.dp)
            )
            .heightIn(min = 36.dp)
            .padding(
                horizontal = MaterialTheme.spacing.large,
                vertical = MaterialTheme.spacing.small
            ),
        contentAlignment = Alignment.Center
    ) {
        if (text.isEmpty())
            LottieAnimationView(stringResource(if (isUser) R.string.ai_chat_animation_white else R.string.ai_chat_animation_gray))
        else
            Text(
                text = text,
                color = textColor,
                style = MaterialTheme.typography.bodyLarge
            )
    }
}

@Composable
private fun VoiceInputBar(
    voiceInputInfo: VoiceInputInfo,
    onAction: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                horizontal = MaterialTheme.spacing.medium,
                vertical = MaterialTheme.spacing.large
            ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        when (voiceInputInfo.state) {
            VoiceState.STOPPED,
            VoiceState.IDLE -> {
                IconButton(onClick = onAction) {
                    Icon(
                        painter = painterResource(id = R.drawable.icon_ai_idle),
                        contentDescription = "Mic"
                    )
                }
                Text(
                    text = stringResource(id = R.string.ai_voice_input_idle),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }

            VoiceState.STARTED,
            VoiceState.PROGRESS -> {
                if (voiceInputInfo.fromUser) {
                    IconButton(onClick = onAction) {
                        LottieAnimationView(fileName = stringResource(R.string.ai_listening_animation))
                    }
                    Text(
                        text = stringResource(id = R.string.ai_voice_input_listening),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                } else {
                    IconButton(onClick = onAction) {
                        Icon(
                            painter = painterResource(id = R.drawable.icon_ai_voice_interupt),
                            contentDescription = "Interrupt"
                        )
                    }
                    Text(
                        text = stringResource(id = R.string.ai_voice_input_interrupt),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewAIChatScreen() {
    M3AppTheme {
        AIChatScreen(
            messages = persistentListOf(
                AIChatMessage("1", "Hello, how can I help you?", AIChatSender.AI),
                AIChatMessage("2", "What do I need to get started?", AIChatSender.USER),
                AIChatMessage(
                    "3",
                    "You'll need flour, eggs, and a pinch of salt.",
                    AIChatSender.AI
                ),
                AIChatMessage("4", "And how do I mix them together?", AIChatSender.USER),
                AIChatMessage(
                    "5",
                    "First, make a mound of flour on a clean surface, then create a well in the center. Crack the eggs into the well and sprinkle a little salt. Use a fork to...",
                    AIChatSender.AI
                )
            ),
            onBackClick = {},
            voiceInputInfo = VoiceInputInfo(true, VoiceState.IDLE),
            onVoiceInputAction = {}
        )
    }
}
