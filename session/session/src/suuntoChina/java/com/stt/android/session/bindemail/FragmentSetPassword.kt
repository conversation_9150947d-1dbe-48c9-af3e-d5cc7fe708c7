package com.stt.android.session.bindemail

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.TextFieldInputWithError
import com.stt.android.login.bindemail.FragmentSetPasswordViewModel
import com.stt.android.resetpassword.SetPasswordError
import com.stt.android.base.BaseContentBody
import com.stt.android.session.base.BaseSessionFragment
import com.stt.android.session.forgetpassword.FragmentForgetPasswordDirections
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class FragmentSetPassword : BaseSessionFragment() {

    private val viewModel: FragmentSetPasswordViewModel by viewModels()
    private val setPasswordArgs: FragmentSetPasswordArgs by navArgs()

    @Composable
    override fun SetContentView() {
        SetPasswordScreen()
    }

    @Composable
    private fun SetPasswordScreen() {
        BackHandler {
            backHandled()
        }
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        if (setPasswordArgs.isReset) {
            LaunchedEffect(uiState.resetPasswordResult) {
                uiState.resetPasswordResult?.let {
                    Timber.d("resetPasswordResult: $it")
                    showSuccessSnackBar {
                        findNavController().navigate(
                            FragmentForgetPasswordDirections.actionContinueWithEmailChina()
                        )
                    }
                }
            }
        } else {
            LaunchedEffect(uiState.setPasswordResult) {
                uiState.setPasswordResult?.let {
                    Timber.d("setPasswordResult: $it")
                    showSuccessSnackBar {
                        activity?.finish()
                    }
                }
            }
        }

        BaseContentBody(
            currentFragment = this@FragmentSetPassword,
            viewModel = viewModel,
            onBackClicked = {
                backHandled()
            }
        ) {
            ContentBody(
                commonUIState.isLoading,
                setPasswordArgs.isReset,
                uiState.newPassword,
                uiState.confirmPassword,
                uiState.setPasswordError,
                onInputNewPassword = {
                    viewModel.inputNewPassword(it)
                },
                onInputConfirmPassword = {
                    viewModel.inputConfirmPassword(it)
                },
                onResetPassword = { isReset, newPassword ->
                    if (isReset) {
                        viewModel.resetPassword(
                            newPassword,
                            uiState.confirmPassword,
                            setPasswordArgs.token,
                            setPasswordArgs.phoneNumber
                        )
                    } else {
                        viewModel.setPassword(
                            setPasswordArgs.email,
                            newPassword,
                            uiState.confirmPassword,
                            setPasswordArgs.token
                        )
                    }
                }
            )
        }
    }

    private fun backHandled() {
        if (setPasswordArgs.isReset) {
            findNavController().navigate(
                FragmentForgetPasswordDirections.actionContinueWithEmailChina()
            )
        } else {
            activity?.finish()
        }
    }

    private fun showSuccessSnackBar(callback: () -> Unit) {
        Snackbar.make(
            requireView(),
            R.string.change_successful,
            Snackbar.LENGTH_LONG
        ).apply {
            addCallback(object : Snackbar.Callback() {
                override fun onDismissed(transientBottomBar: Snackbar?, event: Int) {
                    callback.invoke()
                }
            })
            show()
        }
    }
}

@Preview
@Composable
private fun SetPasswordPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun ContentBody(
    isLoading: Boolean = false,
    isReset: Boolean = false,
    newPassword: String = "",
    confirmPassword: String = "",
    setPasswordError: SetPasswordError? = null,
    onInputNewPassword: (String) -> Unit = {},
    onInputConfirmPassword: (String) -> Unit = {},
    onResetPassword: (Boolean, String) -> Unit = { _, _ -> },
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top,
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xlarge))
        Text(
            stringResource(
                id = if (isReset) R.string.reset_password_title else R.string.set_password_title
            ),
            style = MaterialTheme.typography.bodyXLargeBold,
            textAlign = TextAlign.Center,
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = MaterialTheme.spacing.xlarge,
                    bottom = MaterialTheme.spacing.medium
                ),
            horizontalArrangement = Arrangement.Center,
        ) {
            TextFieldInputWithError(
                currentText = newPassword,
                placeholderText = stringResource(id = R.string.input_new_password),
                onChanged = { onInputNewPassword.invoke(it) },
                errorMessage = setPasswordError?.let { stringResource(id = it.resId) } ?: "",
                keyboardType = KeyboardType.Password,
                isPassword = true
            )
        }

        TextFieldInputWithError(
            currentText = confirmPassword,
            placeholderText = stringResource(id = R.string.input_password_again),
            onChanged = {
                onInputConfirmPassword.invoke(it)
            },
            keyboardType = KeyboardType.Password,
            errorMessage = setPasswordError?.let { stringResource(id = it.resId) } ?: "",
            tipsText = stringResource(id = R.string.input_password_tips),
            isPassword = true
        )
        val keyboardController = LocalSoftwareKeyboardController.current
        PrimaryButton(
            enabled = newPassword.isNotEmpty() && confirmPassword.isNotEmpty(),
            onClick = {
                onResetPassword.invoke(isReset, newPassword)
                keyboardController?.hide()
            },
            text = stringResource(R.string.continue_str),
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.large
                )
        )
    }
    LoadingContent(isLoading)
}
