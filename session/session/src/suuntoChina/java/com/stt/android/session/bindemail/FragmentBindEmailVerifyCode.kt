package com.stt.android.session.bindemail

import androidx.activity.compose.BackHandler
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.R
import com.stt.android.base.BaseContentBody
import com.stt.android.compose.theme.AppTheme
import com.stt.android.newemail.BindEmailVerifyCodeBody
import com.stt.android.newemail.FragmentBindEmailVerifyCodeViewModel
import com.stt.android.session.base.BaseSessionFragment
import com.stt.android.utils.CustomTabsUtils
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class FragmentBindEmailVerifyCode : BaseSessionFragment() {

    private val viewModel: FragmentBindEmailVerifyCodeViewModel by viewModels()
    private val bindEmailVerifyCodeArgs: FragmentBindEmailVerifyCodeArgs by navArgs()

    @Composable
    override fun SetContentView() {
        BindEmailVerifyCodeScreen()
    }

    @Composable
    private fun BindEmailVerifyCodeScreen() {
        BackHandler {
            backHandled()
        }
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        LaunchedEffect(uiState.emailVerifyToken) {
            uiState.emailVerifyToken?.let {
                Timber.d("token: $it")
                findNavController().navigate(
                    FragmentBindEmailVerifyCodeDirections.actionSetPasswordFragment()
                        .setToken(it)
                        .setEmail(bindEmailVerifyCodeArgs.email)
                )
            }
        }

        BaseContentBody(
            currentFragment = this@FragmentBindEmailVerifyCode,
            viewModel = viewModel,
            onBackClicked = {
                backHandled()
            }
        ) {
            ContentBody(
                bindEmailVerifyCodeArgs.email,
                commonUIState.isLoading,
                uiState.verifyCode,
                uiState.verifyCodeIsExpired,
                onInputVerifyCode = {
                    viewModel.inputVerifyCode(it)
                },
                onVerificationCodeClicked = { email, verifyCode ->
                    viewModel.checkVerificationCode(email, verifyCode)
                },
                onResendClicked = {
                    viewModel.sendEmailVerificationCode(bindEmailVerifyCodeArgs.email)
                },
                onContactClicked = {
                    activity?.apply {
                        CustomTabsUtils.launchCustomTab(this, getString(R.string.contact_support_link_suunto))
                    }
                },
            )
        }
    }

    private fun backHandled() {
        findNavController().popBackStack()
    }
}

@Preview
@Composable
private fun BindEmailVerifyCodeScreenPreview() {
    AppTheme {
        Surface {
            ContentBody()
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun ContentBody(
    email: String = "",
    isLoading: Boolean = false,
    verifyCode: String = "",
    verifyCodeIsExpired: Boolean = false,
    btnEnabled: Boolean = false,
    onInputVerifyCode: (String) -> Unit = {},
    onVerificationCodeClicked: (String, String) -> Unit = { _, _ -> },
    onResendClicked: () -> Unit = {},
    onContactClicked: () -> Unit = {},
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    BindEmailVerifyCodeBody(
        isLoading,
        verifyCode,
        verifyCodeIsExpired = verifyCodeIsExpired,
        email,
        onInputVerifyCode = onInputVerifyCode,
        onVerificationCodeClicked = {
            keyboardController?.hide()
            onVerificationCodeClicked.invoke(email, verifyCode)
        },
        onResendClicked = onResendClicked,
        onContactClicked = onContactClicked
    )
}
