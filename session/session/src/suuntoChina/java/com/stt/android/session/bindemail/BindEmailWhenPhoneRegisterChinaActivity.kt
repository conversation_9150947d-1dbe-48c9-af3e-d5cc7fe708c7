package com.stt.android.session.bindemail

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import com.stt.android.newemail.BaseComposeActivity
import com.stt.android.session.R

class BindEmailWhenPhoneRegisterChinaActivity : BaseComposeActivity() {
    companion object {
        private const val USER_KEY = "USER_KEY"
        fun newStartIntent(context: Context, userKey: String): Intent {
            return Intent(context, BindEmailWhenPhoneRegisterChinaActivity::class.java).putExtra(USER_KEY, userKey)
        }
    }

    override fun getNavGraph(): Int {
        return R.navigation.bind_email_when_phone_register
    }

    @Deprecated("Deprecated in Java")
    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        finish()
    }
}
