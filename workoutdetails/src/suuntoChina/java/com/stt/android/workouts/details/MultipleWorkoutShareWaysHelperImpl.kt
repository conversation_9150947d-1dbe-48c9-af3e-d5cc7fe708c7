package com.stt.android.workouts.details

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import coil3.imageLoader
import coil3.request.ImageRequest
import coil3.request.transformations
import coil3.toBitmap
import com.google.maps.android.PolyUtil
import com.sina.weibo.sdk.api.MultiImageObject
import com.sina.weibo.sdk.api.VideoSourceObject
import com.sina.weibo.sdk.api.WebpageObject
import com.sina.weibo.sdk.api.WeiboMultiMessage
import com.sina.weibo.sdk.common.UiError
import com.sina.weibo.sdk.share.WbShareCallback
import com.stt.android.R
import com.stt.android.ShareTargetIdConstant.DOU_YIN_APP_ID
import com.stt.android.ShareTargetIdConstant.TARGET_WECHAT
import com.stt.android.ShareTargetIdConstant.TARGET_WECHAT_MOMENTS
import com.stt.android.ShareTargetIdConstant.WEI_BO_APP_ID
import com.stt.android.ShareTargetIdConstant.WE_CHAT_APP_ID
import com.stt.android.ShareTargetIdConstant.XIAO_HONG_SHU_APP_ID
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.coil.CropTransformation
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.sharepreview.FetchWorkoutSharePreviewMetadataUseCase
import com.stt.android.domain.workouts.sharepreview.WorkoutSharePreviewMetadata
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.multimedia.MediaType
import com.stt.android.multimedia.sportie.SportieSelection
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.multimedia.sportie.SportieShareType
import com.stt.android.network.interfaces.ANetworkProvider
import com.stt.android.sharingplatform.DouYinAPI
import com.stt.android.sharingplatform.ShareResultHandler
import com.stt.android.sharingplatform.SharingResultState
import com.stt.android.sharingplatform.WeChatAPI
import com.stt.android.sharingplatform.WeiboAPI
import com.stt.android.sharingplatform.XhsAPI
import com.stt.android.workout.details.screenshot.ScreenshotObserverImpl
import com.stt.android.workout.details.share.MultipleWaysWorkoutShareActivity
import com.stt.android.ui.extensions.supportWorkoutAnalysisOnMap
import com.stt.android.workouts.sharepreview.WorkoutShareHelperImpl
import com.stt.android.workouts.sharepreview.customshare.ShareTarget
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX
import com.tencent.mm.opensdk.modelmsg.WXImageObject
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage
import com.tencent.mm.opensdk.modelmsg.WXWebpageObject
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.io.FileOutputStream
import javax.inject.Inject

class MultipleWorkoutShareWaysHelperImpl @Inject constructor(
    override val emarsysAnalytics: EmarsysAnalytics,
    override val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    override val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val weChatAPI: WeChatAPI,
    private val weiboAPI: WeiboAPI,
    private val xhsAPI: XhsAPI,
    private val douYinAPI: DouYinAPI,
    private val fetchWorkoutSharePreviewMetadataUseCase: FetchWorkoutSharePreviewMetadataUseCase,
    dispatchers: CoroutinesDispatchers
) : WorkoutShareHelperImpl(
    emarsysAnalytics,
    firebaseAnalyticsTracker,
    amplitudeAnalyticsTracker,
    dispatchers
) {

    private var weiboShareResultHandler: ShareResultHandler? = null

    private val wbShareCallback = object : WbShareCallback {
        override fun onComplete() {
            weiboShareResultHandler?.invoke(SharingResultState.Success)
        }

        override fun onError(p0: UiError?) {
            Timber.w("share to weibo fail: ${p0?.errorMessage}")
            weiboShareResultHandler?.invoke(SharingResultState.Success)
        }

        override fun onCancel() {
            weiboShareResultHandler?.invoke(SharingResultState.Cancel)
        }
    }
    override fun hasCustomIntentHandling(): Boolean = true
    override fun showMultipleWorkoutShareWays(): Boolean = true
    override fun toMultipleWorkoutShareWays(
        context: Context,
        workoutHeader: WorkoutHeader,
        imageIndex: Int,
        watchName: String,
        byScreenshot: Boolean,
    ) {
        val points = workoutHeader.polyline
            ?.takeUnless(String::isEmpty)
            ?.let(PolyUtil::decode)
            ?: emptyList()
        val intentAndOptions = MultipleWaysWorkoutShareActivity.getIntent(
            workoutHeaderId = workoutHeader.id,
            context = context,
            itemIndex = imageIndex,
            workoutDetails = if (byScreenshot) SportieShareSource.WORKOUT_DETAILS_SCREENSHOT else SportieShareSource.WORKOUT_DETAILS,
            watchName = watchName,
            supportVideoShare = workoutHeader.supportWorkoutAnalysisOnMap(points)
        )

        val intent = intentAndOptions.first
        val options = intentAndOptions.second
        if (intent != null && options != null) {
            context.startActivity(intent, options.toBundle())
        }
    }

    override fun observeScreenshot(activity: Activity) {
        if (supportsHandleScreenshot(activity)) {
            (activity as? LifecycleOwner)?.lifecycle?.addObserver(ScreenshotObserverImpl(activity))
        }
    }

    private fun supportsHandleScreenshot(context: Context): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE
            || ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.READ_EXTERNAL_STORAGE
        ) == PackageManager.PERMISSION_GRANTED
            || (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.READ_MEDIA_IMAGES
        ) == PackageManager.PERMISSION_GRANTED)
    }

    override fun getCustomShareImageTargets(context: Context): List<ShareTarget> {
        return mutableListOf<ShareTarget>(
            ShareTarget.CustomTarget(
                WE_CHAT_APP_ID,
                TARGET_WECHAT,
                R.drawable.share_wechat,
                R.string.we_chat
            )
        ).apply {
            add(
                ShareTarget.CustomTarget(
                    WE_CHAT_APP_ID,
                    TARGET_WECHAT_MOMENTS,
                    R.drawable.share_wechat_moments,
                    R.string.we_chat_moments
                )
            )
            add(
                ShareTarget.CustomTarget(
                    WEI_BO_APP_ID,
                    "",
                    R.drawable.icon_weibo,
                    R.string.weibo
                )
            )
            add(ShareTarget.SaveToMedia)
            add(ShareTarget.DelegateToOS)
        }
    }

    override fun getCustomShareLinkTargets(context: Context): List<ShareTarget> {
        return mutableListOf<ShareTarget>(
            ShareTarget.CustomTarget(
                WE_CHAT_APP_ID,
                TARGET_WECHAT,
                R.drawable.share_wechat,
                R.string.we_chat
            )
        ).apply {
            add(
                ShareTarget.CustomTarget(
                    WE_CHAT_APP_ID,
                    TARGET_WECHAT_MOMENTS,
                    R.drawable.share_wechat_moments,
                    R.string.we_chat_moments
                )
            )
            add(
                ShareTarget.CustomTarget(
                    WEI_BO_APP_ID,
                    "",
                    R.drawable.icon_weibo,
                    R.string.weibo
                )
            )
            add(ShareTarget.DelegateToOS)
        }
    }

    override fun getCustomShareVideoTargets(context: Context): List<ShareTarget> {
        return buildList {
            add(
                ShareTarget.CustomTarget(
                    WE_CHAT_APP_ID,
                    TARGET_WECHAT,
                    R.drawable.share_wechat,
                    R.string.we_chat
                )
            )
            add(
                ShareTarget.CustomTarget(
                    WE_CHAT_APP_ID,
                    TARGET_WECHAT_MOMENTS,
                    R.drawable.share_wechat_moments,
                    R.string.we_chat_moments
                )
            )
            add(
                ShareTarget.CustomTarget(
                    WEI_BO_APP_ID,
                    "",
                    R.drawable.icon_weibo,
                    R.string.weibo
                )
            )
            add(
                ShareTarget.CustomTarget(
                    XIAO_HONG_SHU_APP_ID,
                    "",
                    R.drawable.icon_xhs,
                    R.string.xiao_hong_shu_title,
                )
            )
            add(
                ShareTarget.CustomTarget(
                    DOU_YIN_APP_ID,
                    "",
                    R.drawable.icon_douyin,
                    R.string.douyin_title,
                )
            )
            add(ShareTarget.DelegateToOS)
        }
    }

    override fun shareLinkToCustomTarget(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        workoutHeader: WorkoutHeader,
        source: SportieShareSource
    ) {
        // replace global scope with custom coroutineScope
        launch {
            try {
                when (customTarget.appId) {
                    WE_CHAT_APP_ID -> {
                        shareLinkToWeChat(activity, customTarget, workoutHeader, source)
                    }
                    WEI_BO_APP_ID -> {
                        shareLinkToWeibo(activity, workoutHeader, source)
                    }
                    else -> {
                        Timber.w("Custom target not supported: $customTarget")
                    }
                }
            } catch (e: Throwable) {
                Timber.e(e, "Error during shareLinkToCustomTarget to WeChat")
            }
        }
    }

    override fun shareImageToCustomTarget(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        header: WorkoutHeader,
        imageUri: Uri,
        sportieSelection: SportieSelection
    ) {
        try {
            when (customTarget.appId) {
                WE_CHAT_APP_ID -> {
                    shareImageToWeChat(activity, customTarget, header, imageUri, sportieSelection)
                }
                WEI_BO_APP_ID -> {
                    shareImageToWeibo(activity, imageUri, header, sportieSelection)
                }
                else -> {
                    Timber.w("Custom target not supported: $customTarget")
                }
            }
        } catch (e: Throwable) {
            Timber.e(e, "Error during shareImageToCustomTarget to WeChat")
        }
    }

    override fun shareVideoToCustomTarget(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        videoUri: Uri,
        shareType: SportieShareType,
    ) {
        try {
            when (customTarget.appId) {
                WE_CHAT_APP_ID -> {
                    shareVideoToWeChat(activity, videoUri, shareType)
                }
                WEI_BO_APP_ID -> {
                    shareVideoToWeibo(activity, videoUri, shareType)
                }
                XIAO_HONG_SHU_APP_ID -> {
                    shareVideoToXhs(activity, videoUri, shareType)
                }
                DOU_YIN_APP_ID -> {
                    shareVideoToDouyin(activity, videoUri, shareType)
                }
                else -> {
                    Timber.w("Custom target not supported: $customTarget")
                }
            }
        } catch (e: Throwable) {
            Timber.e(e, "Error during shareVideoToCustomTarget to Custom Target")
        }
    }

    private suspend fun shareLinkToWeChat(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        workoutHeader: WorkoutHeader,
        source: SportieShareSource
    ) {
        val shareLinkData = getShareLinkData(workoutHeader, activity)
        val metadata = shareLinkData.first
        val imageBitmap = shareLinkData.second
        withContext(dispatchers.io) {
            val url = ANetworkProvider.buildSecureWebWorkoutUrl(
                workoutHeader.username,
                workoutHeader.key,
                source
            )

            val msg = WXMediaMessage().apply {
                mediaObject = WXWebpageObject(url)
                if (imageBitmap != null) setThumbImage(imageBitmap)
                title = metadata.title
                description = metadata.description
            }
            val req = SendMessageToWX.Req().apply {
                scene = when (customTarget.targetId) {
                    TARGET_WECHAT -> {
                        SendMessageToWX.Req.WXSceneSession
                    }
                    TARGET_WECHAT_MOMENTS -> {
                        SendMessageToWX.Req.WXSceneTimeline
                    }
                    else -> 0
                }
                transaction = "share_workout_link_${workoutHeader.key}"
                message = msg
            }
            weChatAPI.api.sendReq(req)
            sendLinkShareAnalytics(
                source = source,
                analyticsTarget = when (customTarget.targetId) {
                    TARGET_WECHAT -> ANALYTICS_TARGET_WECHAT
                    TARGET_WECHAT_MOMENTS -> ANALYTICS_TARGET_WECHAT_MOMENTS
                    else -> ""
                }
            )
        }
    }

    private suspend fun getShareLinkData(workoutHeader: WorkoutHeader, activity: Activity): Pair<WorkoutSharePreviewMetadata, Bitmap?> {
        val metadata =
            fetchWorkoutSharePreviewMetadataUseCase.fetchWorkoutSharePreviewMetadata(
                workoutHeader.username,
                workoutHeader.key
                    ?: throw IllegalArgumentException("Workout key cannot be null")
            )

        val imageBitmap: Bitmap? = runCatching {
            val request = ImageRequest.Builder(activity)
                .data(metadata.imageUrl)
                .transformations(CropTransformation(CropTransformation.CropType.CENTER))
                .size(400)
                .build()
            activity.imageLoader.execute(request).image?.toBitmap()
        }.onFailure {
            Timber.w(it, "Error loading image for WeChat")
        }.getOrNull()
        return metadata to imageBitmap
    }

    private fun shareImageToWeChat(
        activity: Activity,
        customTarget: ShareTarget.CustomTarget,
        header: WorkoutHeader,
        imageUri: Uri,
        sportieSelection: SportieSelection
    ) {
        activity.grantUriPermission(
            WE_CHAT_APP_ID,
            imageUri,
            Intent.FLAG_GRANT_READ_URI_PERMISSION
        )
        val msg = WXMediaMessage().apply {
            mediaObject = WXImageObject().apply { imagePath = imageUri.toString() }
        }
        val req = SendMessageToWX.Req().apply {
            scene = when (customTarget.targetId) {
                TARGET_WECHAT -> {
                    SendMessageToWX.Req.WXSceneSession
                }
                TARGET_WECHAT_MOMENTS -> {
                    SendMessageToWX.Req.WXSceneTimeline
                }
                else -> 0
            }
            transaction = "share_workout_image_${header.key}"
            message = msg
        }
        weChatAPI.api.sendReq(req)
        sendImageShareAnalytics(
            workoutHeader = header,
            sportieSelection = sportieSelection,
            analyticsTarget = when (customTarget.targetId) {
                TARGET_WECHAT -> ANALYTICS_TARGET_WECHAT
                TARGET_WECHAT_MOMENTS -> ANALYTICS_TARGET_WECHAT_MOMENTS
                else -> ""
            }
        )
    }

    private fun shareImageToWeibo(
        activity: Activity,
        uri: Uri,
        workoutHeader: WorkoutHeader,
        sportieSelection: SportieSelection
    ) {
        val message = WeiboMultiMessage()
        val imageObject = MultiImageObject()
        imageObject.imageList = arrayListOf(uri)
        message.multiImageObject = imageObject
        weiboAPI.api.shareMessage(activity, message, false)
        sendImageShareAnalytics(
            workoutHeader = workoutHeader,
            sportieSelection = sportieSelection,
            analyticsTarget = ANALYTICS_TARGET_WEIBO
        )
    }

    private fun shareVideoToWeChat(activity: Activity, videoUri: Uri, shareType: SportieShareType) {
        MediaStoreUtils.saveMediaToMediaStore(
            activity.contentResolver,
            "workout_video.mp4",
            MediaType.VIDEO,
        ) { fileDescriptor ->
            FileOutputStream(fileDescriptor.fileDescriptor).use { outputStream ->
                activity.contentResolver.openInputStream(videoUri)?.use { inputStream ->
                    inputStream.copyTo(outputStream)
                }
            }
        }
        Toast.makeText(activity, R.string.video_save_finished, Toast.LENGTH_SHORT).show()
        weChatAPI.api.openWXApp()
        sendVideoShareAnalytics(shareType, ANALYTICS_TARGET_WECHAT)
    }

    private fun shareVideoToWeibo(activity: Activity, videoUri: Uri, shareType: SportieShareType) {
        val message = WeiboMultiMessage()
        message.videoSourceObject = VideoSourceObject().apply {
            videoPath = videoUri
        }
        weiboAPI.api.shareMessage(activity, message, false)
        sendVideoShareAnalytics(shareType, ANALYTICS_TARGET_WEIBO)
    }

    private fun shareVideoToXhs(activity: Activity, videoUri: Uri, shareType: SportieShareType) {
        xhsAPI.share(listOf(videoUri), activity.applicationContext, video = true)
        sendVideoShareAnalytics(shareType, ANALYTICS_TARGET_XHS)
    }

    private fun shareVideoToDouyin(activity: Activity, videoUri: Uri, shareType: SportieShareType) {
        douYinAPI.share(listOf(videoUri), activity, video = true)
        sendVideoShareAnalytics(shareType, ANALYTICS_TARGET_DOUYIN)
    }

    private fun shareLinkToWeibo(
        activity: Activity,
        workoutHeader: WorkoutHeader,
        source: SportieShareSource
    ) {
        launch {
            val shareLinkData = getShareLinkData(workoutHeader, activity)
            val metadata = shareLinkData.first
            val imageBitmap = shareLinkData.second
            val message = WeiboMultiMessage()
            val webObject = WebpageObject()
            webObject.title = metadata.title
            webObject.description = metadata.description
            ByteArrayOutputStream().use {
                imageBitmap?.compress(Bitmap.CompressFormat.JPEG, 85, it)
                webObject.thumbData = it.toByteArray()
            }
            val url = ANetworkProvider.buildSecureWebWorkoutUrl(
                workoutHeader.username,
                workoutHeader.key,
                source
            )
            webObject.actionUrl = url
            message.mediaObject = webObject
            weiboAPI.api.shareMessage(activity, message, false)
            sendLinkShareAnalytics(
                source = source,
                analyticsTarget = ANALYTICS_TARGET_WEIBO
            )
        }
    }

    override fun handleWeiboShareResult(intent: Intent, shareResultHandler: ShareResultHandler?) {
        this.weiboShareResultHandler = shareResultHandler
        weiboAPI.api.doResultIntent(
            intent,
            wbShareCallback
        )
    }

    override fun setWeChatShareResultHandler(handler: ShareResultHandler?) {
        weChatAPI.setShareResultHandler(handler)
    }

    override fun hasSharePlatformApp(activity: Activity, appId: String): Boolean {
        return when (appId) {
            WE_CHAT_APP_ID -> weChatAPI.api.isWXAppInstalled
            WEI_BO_APP_ID -> weiboAPI.api.isWBAppInstalled
            XIAO_HONG_SHU_APP_ID -> xhsAPI.supportedShareNote(activity.applicationContext)
            DOU_YIN_APP_ID -> douYinAPI.supportedSharing(activity)
            else -> false
        }
    }

    override fun support3DVideo() : Boolean = true

    override fun supportLongScreenshot(): Boolean = true

    companion object {
        private const val ANALYTICS_TARGET_WECHAT = "WeChat"
        private const val ANALYTICS_TARGET_WECHAT_MOMENTS = "WeChatMoments"
        private const val ANALYTICS_TARGET_WEIBO = "Weibo"
        private const val ANALYTICS_TARGET_XHS = "Xiaohongshu"
        private const val ANALYTICS_TARGET_DOUYIN = "Douyin"
    }
}
