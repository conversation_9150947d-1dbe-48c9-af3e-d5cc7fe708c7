package com.stt.android.workout.details.laps.advanced.table.composables

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Switch
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.theme.switchColors
import com.stt.android.workout.details.R

@Composable
fun LapCellColorInfo(
    showLapsTableColouringToggle: Boolean,
    isLapsTableColouringEnabled: Boolean,
    showNotEnoughRowsText: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .padding(
                start = MaterialTheme.spacing.medium,
                top = MaterialTheme.spacing.small,
                bottom = MaterialTheme.spacing.small,
            )
            .animateContentSize()
    ) {
        if (showLapsTableColouringToggle) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.lap_cell_switch_title),
                    style = MaterialTheme.typography.bodyLarge,
                )
                Switch(
                    modifier = Modifier.padding(end = MaterialTheme.spacing.small),
                    checked = isLapsTableColouringEnabled,
                    colors = MaterialTheme.colors.switchColors,
                    onCheckedChange = {
                        onCheckedChange.invoke(it)
                    })
            }
        }

        AnimatedVisibility(
            visible = isLapsTableColouringEnabled
        ) {
            Column(modifier = Modifier.padding(end = MaterialTheme.spacing.medium)) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = stringResource(id = R.string.lap_cell_color_indicator_lowest),
                        style = MaterialTheme.typography.bodySmall,
                    )
                    Text(
                        text = stringResource(id = R.string.lap_cell_color_indicator_median),
                        style = MaterialTheme.typography.bodySmall,
                    )
                    Text(
                        text = stringResource(id = R.string.lap_cell_color_indicator_highest),
                        style = MaterialTheme.typography.bodySmall,
                    )
                }

                Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(MaterialTheme.spacing.medium)
                        .background(
                            brush = Brush.horizontalGradient(
                                0.10f to colorResource(id = R.color.laps_cell_blue),
                                0.45f to Color.White,
                                0.55f to Color.White,
                                0.90f to colorResource(id = R.color.laps_cell_red)
                            )
                        )
                )

                if (showNotEnoughRowsText) {
                    Text(
                        text = stringResource(id = R.string.lap_cell_not_enough_rows),
                        style = MaterialTheme.typography.body,
                        color = MaterialTheme.colors.darkGrey,
                        modifier = Modifier.padding(vertical = MaterialTheme.spacing.small)
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun LapCellInfoPreview(
) {
    AppTheme {
        LapCellColorInfo(
            showLapsTableColouringToggle = true,
            isLapsTableColouringEnabled = true,
            showNotEnoughRowsText = true,
            onCheckedChange = {}
        )
    }
}
