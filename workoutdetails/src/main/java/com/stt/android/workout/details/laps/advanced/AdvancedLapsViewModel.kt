package com.stt.android.workout.details.laps.advanced

import androidx.annotation.VisibleForTesting
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import com.stt.android.TestOpen
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.toOnOff
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.common.viewstate.ViewState
import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.domain.advancedlaps.FetchLapsTableColumnStatesUseCase
import com.stt.android.domain.advancedlaps.LapsTable
import com.stt.android.domain.advancedlaps.LapsTableColouringEnabledUseCase
import com.stt.android.domain.advancedlaps.LapsTableRow
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.advancedlaps.SaveLapsTableColumnsStatesUseCase
import com.stt.android.domain.advancedlaps.WindowType
import com.stt.android.domain.advancedlaps.rowValue
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.isMultisport
import com.stt.android.infomodel.ActivityMapping
import com.stt.android.infomodel.ActivitySummary_fallback
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.mapping.getActivitySummaryForActivityId
import com.stt.android.mapping.getSummaryCategoriesByStId
import com.stt.android.ui.utils.LiveEvent
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.workout.details.AdvancedLapsData
import com.stt.android.workout.details.LapPageChangeListener
import com.stt.android.workout.details.advancedlaps.AdvancedLapsDataLoader
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.graphanalysis.laps.AnalysisLapsLoader
import com.stt.android.workout.details.graphanalysis.laps.LapMarkerModel
import com.stt.android.workout.details.laps.advanced.table.AdvancedLapsSelectColumnRequest
import com.stt.android.workout.details.laps.advanced.table.AdvancedLapsTable
import com.stt.android.workout.details.laps.advanced.table.AdvancedLapsTableContainer
import com.stt.android.workout.details.laps.advanced.table.AdvancedLapsTableHeaderItem
import com.stt.android.workout.details.laps.advanced.table.AdvancedLapsTableItems
import com.stt.android.workout.details.laps.advanced.table.AdvancedLapsTableRowItem
import com.stt.android.workout.details.laps.advanced.table.LapColumnPercentiles
import com.stt.android.workout.details.laps.advanced.table.LapsColumnData
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.workoutheader.WorkoutHeaderLoader
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Flowable
import io.reactivex.Maybe
import io.reactivex.Scheduler
import io.reactivex.Single
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.abs
import kotlin.math.ceil

@TestOpen
@HiltViewModel
class AdvancedLapsViewModel
@Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val advancedLapsDataLoader: AdvancedLapsDataLoader,
    private val analysisLapsLoader: AnalysisLapsLoader,
    private val workoutHeaderLoader: WorkoutHeaderLoader,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val fetchLapsTableColumnStatesUseCase: FetchLapsTableColumnStatesUseCase,
    private val saveLapsTableColumnsStatesUseCase: SaveLapsTableColumnsStatesUseCase,
    private val lapsTableColouringEnabledUseCase: LapsTableColouringEnabledUseCase,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val infoModelFormatter: InfoModelFormatter,
    private val lapMarkerModel: LapMarkerModel,
    private val dispatchers: CoroutinesDispatchers,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler
) : LoadingStateViewModel<AdvancedLapsData>(ioThread, mainThread) {

    var isLapMarkerEnabled: Boolean
        get() = lapMarkerModel.isLapMarkerEnabled
        set(value) {
            lapMarkerModel.isLapMarkerEnabled = value
        }

    private var currentLapTable: Int =
        savedStateHandle[SAVED_DATA_CURRENT_LAP_TABLE] ?: 0
        set(value) {
            field = value
            savedStateHandle[SAVED_DATA_CURRENT_LAP_TABLE] = value
        }

    val onSelectDataRequested: LiveData<LiveEvent<AdvancedLapsSelectColumnRequest>>
        get() = _onSelectDataRequested
    private val _onSelectDataRequested = MutableLiveData<LiveEvent<AdvancedLapsSelectColumnRequest>>()

    val onLapSelected: LiveData<Pair<LapsTableType, LapsTableRow>>
        get() = _onLapSelected
    private val _onLapSelected = SingleLiveEvent<Pair<LapsTableType, LapsTableRow>>()

    @VisibleForTesting
    var container: AdvancedLapsTableContainer? = null
    private var lapsTables: List<LapsTable>? = null
    private var stId: Int = ActivityMapping.UNSPECIFIEDSPORT.stId
    private var isFullMultisport = false
    private var isPartOfMultiSport = false
    private val availableColumns = mutableMapOf<Int?, MutableMap<LapsTableType, List<LapsTableDataType>>>()

    private var loadJob: Job? = null
    var selectLapsTableType: LapsTableType? = null
    var selectLapsTableRow: LapsTableRow? = null
    fun setSelectedLap(lapsTableType: LapsTableType?, lapsTableRow: LapsTableRow?) {
        this.selectLapsTableType = lapsTableType
        this.selectLapsTableRow = lapsTableRow
    }
    /**
     *  longScreenshotLayout = true, use different logic to calculate current tab
     */
    fun loadData(
        useAnalysisLaps: Boolean = false,
        longScreenshotLayout: Boolean = false,
    ) {
        loadJob?.cancel()
        notifyLoading()
        loadJob = launch(dispatchers.io) {
            combine(
                workoutHeaderLoader.workoutHeaderFlow
                    .mapNotNull { it.data },
                multisportPartActivityLoader.multisportPartActivityFlow
                    .filter { it.isLoaded() },
                ::Pair
            )
                .flatMapLatest { (workoutHeader, multisportPartActivityState) ->
                    val multisportPartActivity = multisportPartActivityState.data
                    stId = multisportPartActivity?.activityType ?: workoutHeader.activityTypeId
                    isFullMultisport = workoutHeader.isMultisport && multisportPartActivity == null
                    isPartOfMultiSport = multisportPartActivity != null

                    if (useAnalysisLaps) {
                        analysisLapsLoader.loadLapsTables(workoutHeader)
                    } else {
                        advancedLapsDataLoader.loadLapsTables(workoutHeader)
                    }
                }.onEach {
                    onLapsTablesLoaded(it, longScreenshotLayout)
                }.collect()
        }
    }

    /**
     * longScreenshot logic for current table index:
     * If all lap data items size  > 10, display the items of the smallest
     * if all lap data items size  < 10, display the items of the largest
     * If lap data items size < 10 and > 10,  display the items of the closest to 10
     */
    private fun getCurrentLapTable(tables: List<AdvancedLapsTableItems>): Int {
        return if (tables.isEmpty()) {
            0
        } else {
            tables.withIndex()
                .minBy { (_, table) -> abs(table.items.size - LAP_ITEMS_THRESHOLD) }.index
        }
    }

    private suspend fun onLapsTablesLoaded(
        lapsTablesState: ViewState<List<LapsTable>?>,
        longScreenshotLayout: Boolean = false
    ) {
        if (isFullMultisport ||
            (lapsTablesState.isLoaded() && lapsTablesState.data == null)
        ) {
            this.container = null
            availableColumns.clear()
            notifyDataLoaded(null)
            return
        }

        val lapsTables = lapsTablesState.data ?: return
        this.lapsTables = lapsTables

        val container = getLapsContainer(
            stId,
            lapsTables,
            longScreenshotLayout
        ).await()

        updateData(stId, container)
    }

    @VisibleForTesting
    fun updateData(stId: Int, container: AdvancedLapsTableContainer) {
        this.container = container
        updateAvailableColumns(stId, container)
        notifyDataLoaded(AdvancedLapsData(stId, container, lapPageChangeListener))
    }

    fun getAvailableColumns(stId: Int, lapsTableType: LapsTableType): List<LapsTableDataType>? =
        availableColumns[stId]?.get(lapsTableType)

    private fun updateAvailableColumns(stId: Int, container: AdvancedLapsTableContainer) =
        container.tables.forEach {
            availableColumns.getOrPut(stId) { mutableMapOf() }[it.lapsTableType] =
                it.availableColumns
        }

    private fun getLapsContainer(
        stId: Int,
        lapTables: List<LapsTable>,
        longScreenshotLayout: Boolean,
    ): Single<AdvancedLapsTableContainer> =
        // Loads columns together with lap tables and transforms them into a container used by the UI
        Flowable.just(lapTables)
            .flatMapIterable { it }
            .concatMapEager { table ->
                loadDataTypes(stId, table)
                    .toFlowable()
                    .map { summaryItems ->
                        AdvancedLapsTable(summaryItems, table)
                    }
            }
            .toList()
            .map {
                createItemTables(stId, it)
            }
            .map {
                AdvancedLapsTableContainer(
                    stId = stId,
                    tables = it,
                    currentLapTable = if (longScreenshotLayout) getCurrentLapTable(it) else currentLapTable,
                    isLapsTableColouringEnabled = lapsTableColouringEnabledUseCase.fetchIsLapsTableColouringEnabled(),
                    showLapCellColorInfoUi = true,
                    onSelectColumnRequested = ::onSelectDataRequest,
                    onHighLightVariancesToggled = ::onHighlightVariancesSwitchToggled
                )
            }

    private fun createItemTables(stId: Int, tables: List<AdvancedLapsTable>): List<AdvancedLapsTableItems> {
        return tables.map { createItemTable(stId, it) }
    }

    private fun createItemTable(stId: Int, table: AdvancedLapsTable): AdvancedLapsTableItems {
        val columns = table.availableColumns.take(COLUMN_COUNT)
        // get the lap data types for this workout (define them in activitySummaries.json)
        val lapDataTypes = getSummaryCategoriesByStId(stId, table.lapTable.lapsType).values.flatten().map { LapsTableDataType.Summary(it) }
        // filter the columns that are in the lap data types
        val updatedColumns = columns.intersect(lapDataTypes.toSet()).toList()
        // TODO: Remove this limitation once the lap table performance is enhanced to handle larger datasets
        val tableRows = table.lapTable.lapsTableRows.take(300)
        val columnData = updatedColumns.map { column ->
            val isCellColouringEnabled = isCellColouringEnabled(column = column)

            val percentiles = if (isCellColouringEnabled) {
                val lapsWithoutSubRows = table.lapTable.lapsTableRows.filterNot { it.type == WindowType.POOLLENGTH }
                calculatePercentilesForColumn(column, lapsWithoutSubRows)
            } else null

            LapsColumnData(
                column = column,
                percentiles = percentiles
            )
        }

        val rows = when {
            table.lapTable.lapsType == LapsTableType.INTERVAL -> createIntervalItems(stId, table, columnData)
            else -> tableRows.map { row ->
                createNormalItem(columnData, row, table.lapTable.lapsType, isRecoveryInterval = false)
            }
        }

        val headerItem = createHeaderItem(stId, updatedColumns, table.lapTable.lapsType)
        return AdvancedLapsTableItems(
            table.lapTable,
            table.availableColumns,
            headerItem,
            rows,
            table.lapTable.autoLapLength
        )
    }

    fun isCellColouringEnabled(column: LapsTableDataType): Boolean {
        return !colouringDisabledColumns.contains(column.key)
    }

    private fun createIntervalItems(
        stId: Int,
        table: AdvancedLapsTable,
        columns: List<LapsColumnData>,
    ): List<AdvancedLapsTableRowItem> {
        val subRowsByLapNumber = table.lapTable.lapsTableRows
            .filter { it.type == WindowType.POOLLENGTH }
            .groupBy { it.lapNumber }

        return table.lapTable.lapsTableRows
            .filter { it.type == WindowType.INTERVAL }
            .map { row ->
                val subRows = subRowsByLapNumber[row.lapNumber]
                val activityType = ActivityType.valueOf(stId)
                if (activityType == ActivityType.SWIMMING) {
                    createSwimmingIntervalItem(subRows, columns, row, table)
                } else {
                    createIntervalItem(columns, row, table)
                }
            }
    }

    private fun createSwimmingIntervalItem(
        subRows: List<LapsTableRow>?,
        columns: List<LapsColumnData>,
        row: LapsTableRow,
        table: AdvancedLapsTable
    ): AdvancedLapsTableRowItem =
        // Only show an expandable item when there are more than 1 subRow
        if (subRows.isNullOrEmpty() || subRows.count() == 1) {
            createNormalItem(columns, row, table.lapTable.lapsType, false)
        } else {
            createExpandableItem(columns, row, subRows, table.lapTable.lapsType)
        }

    private fun createIntervalItem(
        columns: List<LapsColumnData>,
        row: LapsTableRow,
        table: AdvancedLapsTable
    ): AdvancedLapsTableRowItem =
        createNormalItem(columns, row, table.lapTable.lapsType, row.isIntervalRecoveryLap ?: false)

    private fun createHeaderItem(
        stId: Int,
        columns: List<LapsTableDataType>,
        lapsTableType: LapsTableType
    ): AdvancedLapsTableHeaderItem {
        return AdvancedLapsTableHeaderItem(
            "$lapsTableType ${AdvancedLapsRowType.Header}",
            stId,
            AdvancedLapsRowType.Header,
            columns
        )
    }

    private fun createNormalItem(
        columns: List<LapsColumnData>,
        row: LapsTableRow,
        lapsTableType: LapsTableType,
        isRecoveryInterval: Boolean
    ): AdvancedLapsTableRowItem {
        return AdvancedLapsTableRowItem(
            id = "$lapsTableType ${AdvancedLapsRowType.Normal} ${row.lapNumber}",
            type = AdvancedLapsRowType.Normal,
            selectedColumns = columns,
            row = row,
            isExpanded = false,
            subRows = emptyList(),
            isRecoveryInterval = isRecoveryInterval,
            onExpandToggled = { },
            onLapSelected = ::onLapRowItemSelected,
            isSelected = row == selectLapsTableRow
        )
    }

    private fun createExpandableItem(
        columns: List<LapsColumnData>,
        row: LapsTableRow,
        subRows: List<LapsTableRow>,
        lapsTableType: LapsTableType
    ): AdvancedLapsTableRowItem {
        return AdvancedLapsTableRowItem(
            id = "$lapsTableType ${AdvancedLapsRowType.Expandable} ${row.lapNumber}",
            type = AdvancedLapsRowType.Expandable,
            selectedColumns = columns,
            row = row,
            isExpanded = false,
            subRows = subRows.mapIndexed { index, subRow ->
                createSubRowItem(columns, index.toString(), subRow, lapsTableType)
            },
            isRecoveryInterval = false,
            onExpandToggled = ::onExpandToggled,
            onLapSelected = ::onLapRowItemSelected,
            isSelected = row == selectLapsTableRow
        )
    }

    private fun createSubRowItem(
        columns: List<LapsColumnData>,
        id: String,
        row: LapsTableRow,
        lapsTableType: LapsTableType
    ): AdvancedLapsTableRowItem {
        return AdvancedLapsTableRowItem(
            id = "$lapsTableType ${AdvancedLapsRowType.SubRow} ${row.lapNumber} $id",
            type = AdvancedLapsRowType.SubRow,
            selectedColumns = columns,
            row = row,
            isExpanded = false,
            subRows = emptyList(),
            onExpandToggled = { },
            onLapSelected = ::onLapRowItemSelected,
            isSelected = row == selectLapsTableRow
        )
    }

    private fun onLapRowItemSelected(lapsTableType: LapsTableType, lapsTableRow: LapsTableRow) {
        _onLapSelected.postValue(lapsTableType to lapsTableRow)
    }

    private fun onExpandToggled(toggledItem: AdvancedLapsTableRowItem) {
        this.container?.let {
            // Creates a copy of the container changing only the value of given toggleItem.isExpanded property
            val tables = it.tables.map { table ->
                val items = table.items.map { rowItem ->
                    if (rowItem.id == toggledItem.id) {
                        rowItem.copy(isExpanded = !rowItem.isExpanded)
                    } else {
                        rowItem
                    }
                }
                table.copy(items = items)
            }
            updateData(it.stId, it.copy(tables = tables))
        }
    }

    private fun onSelectDataRequest(request: LiveEvent<AdvancedLapsSelectColumnRequest>) {
        _onSelectDataRequested.value = request
    }

    private fun onHighlightVariancesSwitchToggled(enabled: Boolean) {
        lapsTableColouringEnabledUseCase.saveIsLapsTableColouringEnabled(enabled)
        sendSwitchToggledAnalytics(enabled)
        this.container?.let {
            updateData(it.stId, it.copy(isLapsTableColouringEnabled = enabled))
        }
    }

    private fun sendSwitchToggledAnalytics(colouringEnabled: Boolean) {
        val activityName = ActivityType.valueOf(stId).simpleName
        val properties = AnalyticsProperties().apply {
            put(AnalyticsEventProperty.ACTIVITY_TYPE, activityName)
            put(AnalyticsEventProperty.LAP_COLOURING_NEW_SETTING, colouringEnabled.toOnOff())
        }
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.LAP_COLOURING_TOGGLED, properties)
    }

    /**
     * Updates a column at [columnIndex] with [summaryItem] for the lap table of type [lapsTableType]
     * Change is persisted to shared preferences
     * @param lapsTableType Defines the table this change affects
     * @param columnIndex Defines the position of the column to be changed
     * @param dataType New column to replace the current one
     */
    fun onSelectDataType(stId: Int, lapsTableType: LapsTableType, columnIndex: Int, dataType: LapsTableDataType, previousDataType: LapsTableDataType) {
        trackLapTableEditColumnsEvent(stId, isPartOfMultiSport, columnIndex, dataType.key, previousDataType.key)

        val lapsTable = lapsTables?.firstOrNull { it.lapsType == lapsTableType }
        val container = container
        val availableColumns = getAvailableColumns(stId, lapsTableType)
        if (lapsTable != null && container != null && availableColumns != null) {
            disposables.add(
                Single
                    .fromCallable {
                        availableColumns.toMutableList().apply {
                            if (size > columnIndex) removeAt(columnIndex)
                            add(columnIndex, dataType)
                        }
                    }
                    .flatMap { items ->
                        saveDataTypeSettings(stId, lapsTableType, items)
                        loadDataTypes(stId, lapsTable)
                    }
                    .map { columns ->
                        getContainerWithUpdateColumns(container, lapsTableType, columns)
                    }
                    .subscribeOn(ioThread)
                    .observeOn(mainThread)
                    .subscribeBy(
                        onSuccess = { updatedContainer ->
                            updateData(stId, updatedContainer)
                        },
                        onError = {
                            notifyError(it)
                            Timber.w(it)
                        }
                    )
            )
        }
    }

    /**
     * Creates a copy of the container by updating summaryItems for all items with given columns.
     */
    private fun getContainerWithUpdateColumns(
        container: AdvancedLapsTableContainer,
        lapsTableType: LapsTableType,
        availableColumns: List<LapsTableDataType>
    ): AdvancedLapsTableContainer {
        val columns = availableColumns.take(COLUMN_COUNT)
        val tables = container.tables.map { table ->
            if (table.lapsTableType == lapsTableType) {
                val tableRows = table.items.map { it.row }
                val columnData = columns.map {
                    val isCellColouringEnabled = isCellColouringEnabled(column = it)
                    LapsColumnData(
                        column = it,
                        percentiles = if (isCellColouringEnabled) calculatePercentilesForColumn(
                            it,
                            tableRows
                        ) else null
                    )
                }
                val items = table.items.map { rowItem ->
                    val subRows =
                        rowItem.subRows.map { subRow -> subRow.copy(selectedColumns = columnData) }
                    rowItem.copy(selectedColumns = columnData, subRows = subRows)
                }
                val headerItem = table.headerItem.copy(selectedColumns = columns)
                table.copy(
                    availableColumns = availableColumns,
                    headerItem = headerItem,
                    items = items
                )
            } else {
                table
            }
        }
        return container.copy(tables = tables)
    }

    private fun loadDataTypes(
        stId: Int,
        lapsTable: LapsTable
    ): Single<List<LapsTableDataType>> {
        return loadDataTypeSettings(stId, lapsTable.lapsType)
            .onErrorReturn {
                Timber.w(it, "Failed to load data types: using defaults")
                emptyList()
            }
            .flatMap { settings ->
                loadDefaultDataTypeSettings(stId, lapsTable.lapsType)
                    .map { defaultSettings ->
                        // Add unused summary items from default settings at the end
                        settings + defaultSettings.filter { !settings.contains(it) }
                    }
                    .toMaybe()
            }
            .switchIfEmpty(
                loadDefaultDataTypeSettings(stId, lapsTable.lapsType)
            )
            // Only summary items with data are needed. Filter the result
            // with table.summaryItems (contains all the summary items that have data)
            // Can't use .intersect because it works on sets and we would lose duplicates i.e. user selects the same
            // data for 2 different columns.
            // We add the remaining available dataTypes from the data (in case they are not returned from the saved list)
            .map { dataTypes ->
                dataTypes.filter { summaryItem -> lapsTable.dataTypes.contains(summaryItem) } +
                    lapsTable.dataTypes.filterNot { dataTypes.contains(it) }
            }
    }

    private fun loadDefaultDataTypeSettings(
        stId: Int,
        lapType: LapsTableType,
    ): Single<List<LapsTableDataType>> {
        return Single.fromCallable {
            val summary = if (!isFullMultisport) {
                getActivitySummaryForActivityId(stId)
            } else {
                ActivitySummary_fallback
            }

            when (lapType) {
                LapsTableType.MANUAL -> summary.manualLaps
                LapsTableType.INTERVAL -> summary.intervalLaps
                LapsTableType.DISTANCE_AUTO_LAP,
                LapsTableType.ONE_KM_AUTO_LAP,
                LapsTableType.FIVE_KM_AUTO_LAP,
                LapsTableType.TEN_KM_AUTO_LAP,
                LapsTableType.ONE_MILE_AUTO_LAP,
                LapsTableType.FIVE_MILE_AUTO_LAP,
                LapsTableType.TEN_MILE_AUTO_LAP -> summary.distanceAutoLaps
                LapsTableType.DURATION_AUTO_LAP -> summary.durationAutoLaps
                LapsTableType.DOWNHILL -> summary.downhillLaps
                LapsTableType.DIVE -> summary.diveAutoLaps
            }
        }.map { summaryTypes -> summaryTypes.map { LapsTableDataType.Summary(it) } }
    }

    fun trackLapTableEditColumnsEvent(
        activityId: Int,
        isPartOfMultisport: Boolean,
        columnNumber: Int,
        newDataType: String,
        oldDataType: String
    ) {
        val activityName = ActivityType.valueOf(activityId).simpleName
        AnalyticsProperties().apply {
            put(AnalyticsEventProperty.ACTIVITY_TYPE, activityName)
            putYesNo(AnalyticsEventProperty.PART_OF_MULTISPORT_WORKOUT, isPartOfMultisport)
            put(AnalyticsEventProperty.COLUMN_CHANGED, columnNumber)
            put(AnalyticsEventProperty.NEW_DATA_TYPE, newDataType)
            put(AnalyticsEventProperty.OLD_DATA_TYPE, oldDataType)

            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.WORKOUT_DETAILS_LAP_TABLE_EDIT_COLUMNS,
                this
            )
        }
    }

    /**
     * Return only a maximum of [COLUMN_COUNT] items. Default items are dynamically added to the end of the list.
     */
    private fun loadDataTypeSettings(stId: Int, lapType: LapsTableType): Maybe<List<LapsTableDataType>> =
        Maybe.fromCallable { fetchLapsTableColumnStatesUseCase(stId, lapType) }
            .map { it.take(COLUMN_COUNT) }

    /**
     * Save only a maximum of [COLUMN_COUNT] items. Default items are dynamically added to the end of the list when
     * the stored items are read back.
     */
    private fun saveDataTypeSettings(stId: Int, lapType: LapsTableType, dataTypes: List<LapsTableDataType>) {
        saveLapsTableColumnsStatesUseCase(stId, lapType, dataTypes.take(COLUMN_COUNT))
    }

    override fun retryLoading() {
        // Not needed.
    }

    private fun calculatePercentilesForColumn(
        column: LapsTableDataType,
        rows: List<LapsTableRow>
    ): LapColumnPercentiles? {
        val rowValues = rows.mapNotNull {
            val rowValue = column.rowValue(it)
            if (rowValue is Number) {
                rowValue.toDouble()
            } else {
                null
            }
        }

        val formattedRowValues: List<Double> = rowValues.mapNotNull { value ->
            when (column) {
                is LapsTableDataType.Summary -> {
                    getSummaryRowValueForCalculations(infoModelFormatter, value, column.summaryItem)
                }

                is LapsTableDataType.SuuntoPlus -> {
                    val formatStyle = column.suuntoPlusChannel.formatStyleForSIM
                    if (formatStyle != null) {
                        getSuuntoPlusRowValueForCalculations(infoModelFormatter, value, formatStyle)
                    } else {
                        null
                    }
                }
            }
        }

        val rowsForCalculation = formattedRowValues
            .ifEmpty { rowValues }
            .distinct()
            .sorted()
        
        if (rowsForCalculation.isEmpty() || rowsForCalculation.toSet().size == 1) {
            return null
        }
        return rowsForCalculation.let {
            LapColumnPercentiles(
                percentile10 = calculatePercentile(it, 10.0),
                percentile45 = calculatePercentile(it, 45.0),
                percentile55 = calculatePercentile(it, 55.0),
                percentile90 = calculatePercentile(it, 90.0),
                areFormattedValuesUsedForCalculation = formattedRowValues.isNotEmpty()
            )
        }
    }

    private fun calculatePercentile(list: List<Double>, percentile: Double): Double {
        val index = ceil(percentile / 100.0 * list.size).toInt()
        return list[index - 1]
    }

    private val colouringDisabledColumns = listOf(
        SummaryItem.CUMULATEDDISTANCE.key,
        SummaryItem.CUMULATEDDURATION.key,
        SummaryItem.DISTANCE.key,
        SummaryItem.DURATION.key,
        SummaryItem.SWIMDISTANCE.key,
        SummaryItem.CUMULATEDSWIMDISTANCE.key
    )

    private val lapPageChangeListener = object : LapPageChangeListener {
        override suspend fun onPageChanged(
            lapsTableType: LapsTableType,
            activityName: String
        ) {
            workoutDetailsAnalytics.trackLapTableChangeIntervalEvent(
                lapsTableType,
                activityName,
                isPartOfMultiSport
            )

            currentLapTable = lapsTables?.indexOfFirst { it.lapsType == lapsTableType } ?: 0
            container?.currentLapTable = currentLapTable
        }
    }

    companion object {
        const val COLUMN_COUNT = 4
        private const val SAVED_DATA_CURRENT_LAP_TABLE = "currentLapTable"
        private const val LAP_ITEMS_THRESHOLD = 10
    }
}
