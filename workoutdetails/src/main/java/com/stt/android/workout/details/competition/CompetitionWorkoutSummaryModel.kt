package com.stt.android.workout.details.competition

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airbnb.epoxy.EpoxyAttribute
import com.airbnb.epoxy.EpoxyModelClass
import com.airbnb.epoxy.EpoxyModelWithHolder
import com.stt.android.common.KotlinEpoxyHolder
import com.stt.android.domain.workouts.WorkoutHeader

import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummaryData
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workout.details.R

@EpoxyModelClass
abstract class CompetitionWorkoutSummaryModel : EpoxyModelWithHolder<SummaryViewHolder>() {

    @EpoxyAttribute
    lateinit var competitionWorkoutSummaryData: CompetitionWorkoutSummaryData

    @EpoxyAttribute
    lateinit var workoutHeader: WorkoutHeader

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var onWorkoutCompetitionClick: View.OnClickListener

    @EpoxyAttribute(EpoxyAttribute.Option.DoNotHash)
    lateinit var infoModelFormatter: InfoModelFormatter

    override fun getDefaultLayout() = R.layout.model_competition_workout_summary

    override fun bind(holder: SummaryViewHolder) {
        val context = holder.workoutCompetitionTitle.context
        if (::competitionWorkoutSummaryData.isInitialized.not()) return
        val competition = competitionWorkoutSummaryData.summary.competition
        holder.workoutCompetitionIcon.setCompetitionIcon(competition?.result)
        holder.workoutCompetitionTitle.setCompetitionTitleText(
            (competition?.finishDuration ?: 0) - (competition?.targetDuration ?: 0), infoModelFormatter, SummaryItem.DURATION
        )
        holder.workoutCompetitionTitle.setCompetitionTitleText(
            workoutHeader.totalDistance.toLong() - (competition?.distance ?: 0).toLong(), infoModelFormatter, SummaryItem.DISTANCE
        )
        holder.workoutCompetitionTitle.setCompetitionTitleColor(context, competition?.result)
        holder.workoutCompetitionSubtitle.setCompetitionSubtitleText(competition?.result, context)
        holder.workoutCompetitionContainer.setOnClickListener(onWorkoutCompetitionClick)
    }
}

class SummaryViewHolder : KotlinEpoxyHolder() {
    val workoutCompetitionTitle by bind<TextView>(R.id.workoutCompetitionTitle)
    val workoutCompetitionSubtitle by bind<TextView>(R.id.workoutCompetitionSubtitle)
    val workoutCompetitionContainer by bind<ConstraintLayout>(R.id.workoutCompetitionContainer)
    val workoutCompetitionIcon by bind<ImageView>(R.id.icon_trophy)
}
