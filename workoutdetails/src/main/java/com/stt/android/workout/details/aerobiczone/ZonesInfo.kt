package com.stt.android.workout.details.aerobiczone

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodyMegaBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.bodyXLargeBold
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.intensityzone.IntensityZone
import com.stt.android.workout.details.R

private val ZoneBarHeight = 132.dp
private val ZoneLimitHeight = 62.dp

@Composable
fun AerobicHeartRateZones(
    aerobicZoneInfoUiState: AerobicZoneInfoUiState,
    onCloseClick: () -> Unit,
    onInfoClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    WorkoutZonesUI(
        title = stringResource(R.string.aerobic_heart_rate_zone_info_title),
        description = stringResource(R.string.aerobic_heart_rate_zone_info_description),
        showShouldUpdateZones = aerobicZoneInfoUiState.showShouldUpdateZones,
        onCloseClick = onCloseClick,
        zones = {
            ZonesForWorkout(
                aerobicZoneInfoUiState = aerobicZoneInfoUiState,
                onInfoClick = onInfoClick,
            )
        },
        modifier = modifier.narrowContent(),
    )
}

@Composable
fun AerobicPaceZones(
    aerobicZoneInfoUiState: AerobicZoneInfoUiState,
    onCloseClick: () -> Unit,
    onInfoClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    WorkoutZonesUI(
        title = stringResource(R.string.aerobic_pace_info_title),
        description = stringResource(R.string.aerobic_pace_zone_info_description),
        showShouldUpdateZones = aerobicZoneInfoUiState.showShouldUpdateZones,
        onCloseClick = onCloseClick,
        zones = {
            ZonesForWorkout(
                aerobicZoneInfoUiState = aerobicZoneInfoUiState,
                onInfoClick = onInfoClick
            )
        },
        modifier = modifier,
    )
}

@Composable
fun AerobicPowerZones(
    aerobicZoneInfoUiState: AerobicZoneInfoUiState,
    onCloseClick: () -> Unit,
    onInfoClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    WorkoutZonesUI(
        title = stringResource(R.string.aerobic_power_info_title),
        description = stringResource(R.string.aerobic_power_zone_info_description),
        showShouldUpdateZones = aerobicZoneInfoUiState.showShouldUpdateZones,
        onCloseClick = onCloseClick,
        zones = {
            ZonesForWorkout(
                aerobicZoneInfoUiState = aerobicZoneInfoUiState,
                onInfoClick = onInfoClick
            )
        },
        modifier = modifier,
    )
}

@Composable
private fun WorkoutZonesUI(
    title: String,
    description: String,
    showShouldUpdateZones: Boolean,
    onCloseClick: () -> Unit,
    zones: @Composable () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colors.surface)
    ) {
        SuuntoIconButton(
            icon = SuuntoIcons.ActionBack,
            onClick = onCloseClick,
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            ScreenTitle(text = title)
            Divider()
            DescriptionText(text = description)
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
            zones()

            if (showShouldUpdateZones) {
                SectionTitle(text = stringResource(R.string.aerobic_zone_should_i_update_my_zones_title))
                Divider()
                DescriptionText(text = stringResource(R.string.aerobic_zone_should_i_update_my_zones_description))
            }

            SectionTitle(text = stringResource(R.string.aerobic_zone_how_to_update_zones))
            Divider()
            HowCanIUpdateMyZones()
        }
    }
}

@Composable
private fun ScreenTitle(
    text: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        style = MaterialTheme.typography.bodyMegaBold,
        modifier = modifier.padding(MaterialTheme.spacing.medium)
    )
}

@Composable
private fun SectionTitle(
    text: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        style = MaterialTheme.typography.bodyXLargeBold,
        modifier = modifier.padding(MaterialTheme.spacing.medium)
    )
}

@Composable
private fun DescriptionText(
    text: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        style = MaterialTheme.typography.bodyLarge,
        modifier = modifier.padding(MaterialTheme.spacing.medium)
    )
}

@Composable
private fun ZonesForWorkout(
    aerobicZoneInfoUiState: AerobicZoneInfoUiState,
    modifier: Modifier = Modifier,
    onInfoClick: (() -> Unit)? = null,
) {
    val context = LocalContext.current
    val hasMaxHr by remember(aerobicZoneInfoUiState) {
        mutableStateOf(aerobicZoneInfoUiState.maxHr != null)
    }
    val topPadding by remember(hasMaxHr) {
        mutableStateOf(if (!hasMaxHr) (ZoneLimitHeight / 2) else 0.dp)
    }
    Column(modifier = modifier.fillMaxWidth()) {
        SectionTitle(text = stringResource(R.string.aerobic_zone_zones_for_this_workout_title))
        Divider()
        if (aerobicZoneInfoUiState.measuredAnaerobicThreshold != null ||
            aerobicZoneInfoUiState.measuredAerobicThreshold != null) {
            // heart_belt_outline
            Row(
                modifier = Modifier.padding(MaterialTheme.spacing.medium),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Icon(
                    painter = painterResource(com.stt.android.R.drawable.heart_belt_outline),
                    contentDescription = null,
                    modifier = Modifier.size(MaterialTheme.iconSizes.small),
                )
                Text(
                    text = stringResource(R.string.aerobic_zone_zones_for_this_workout_description),
                    modifier = Modifier.padding(start = MaterialTheme.spacing.medium),
                    style = MaterialTheme.typography.bodyLarge,
                )
            }
            Divider()
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(691.dp)
                .padding(horizontal = MaterialTheme.spacing.medium)
        ) {
            Column(modifier = Modifier.padding(top = if (hasMaxHr) (ZoneLimitHeight / 2) else 0.dp)) {
                IntensityZone.entries.reversed().forEachIndexed { index, intensityZone ->
                    ZoneItem(
                        zoneNumber = (5 - index),
                        zoneName = stringResource(intensityZone.toAerobicIqTitle()),
                        color = Color(ContextCompat.getColor(context, intensityZone.color)),
                    )
                }
            }

            aerobicZoneInfoUiState.maxHr?.let {
                ZoneLimit(
                    title = stringResource(R.string.aerobic_zone_zones_for_this_workout_max_hr),
                    zoneData = it,
                    unit = aerobicZoneInfoUiState.unit,
                )
            }
            ZoneLimit(
                title = stringResource(R.string.aerobic_zone_zones_for_this_workout_anaerobic_threshold),
                zoneData = aerobicZoneInfoUiState.anaerobicThreshold,
                measuredZoneData = aerobicZoneInfoUiState.measuredAnaerobicThreshold,
                unit = aerobicZoneInfoUiState.unit,
                onInfoClick = onInfoClick,
                modifier = Modifier.offset {
                    IntOffset(0, ZoneBarHeight.minus(topPadding).roundToPx())
                },
            )
            ZoneLimit(
                title = stringResource(R.string.aerobic_zone_zones_for_this_workout_zone_4_minimum),
                zoneData = aerobicZoneInfoUiState.zone4Minimum,
                unit = aerobicZoneInfoUiState.unit,
                modifier = Modifier.offset {
                    IntOffset(0, ZoneBarHeight.times(2).minus(topPadding).roundToPx())
                },
            )
            ZoneLimit(
                title = stringResource(R.string.aerobic_zone_zones_for_this_workout_aerobic_threshold),
                zoneData = aerobicZoneInfoUiState.aerobicThreshold,
                measuredZoneData = aerobicZoneInfoUiState.measuredAerobicThreshold,
                unit = aerobicZoneInfoUiState.unit,
                onInfoClick = onInfoClick,
                modifier = Modifier.offset {
                    IntOffset(0, ZoneBarHeight.times(3).minus(topPadding).roundToPx())
                },
            )
            ZoneLimit(
                title = stringResource(R.string.aerobic_zone_zones_for_this_workout_zone_2_minimum),
                zoneData = aerobicZoneInfoUiState.zone2Minimum,
                unit = aerobicZoneInfoUiState.unit,
                modifier = Modifier.offset {
                    IntOffset(0, ZoneBarHeight.times(4).minus(topPadding).roundToPx())
                }
            )
        }
    }
}

private fun IntensityZone.toAerobicIqTitle(): Int {
    return when (this) {
        IntensityZone.WARMUP -> R.string.aerobic_zone_zones_for_this_workout_light_aerobic
        IntensityZone.ENDURANCE -> R.string.aerobic_zone_zones_for_this_workout_aerobic
        IntensityZone.AEROBIC -> R.string.aerobic_zone_zones_for_this_workout_light_anaerobic
        IntensityZone.ANAEROBIC -> R.string.aerobic_zone_zones_for_this_workout_anaerobic
        IntensityZone.PEAK -> R.string.aerobic_zone_zones_for_this_workout_vo2max
    }
}

@Composable
private fun ZoneItem(
    zoneNumber: Int,
    zoneName: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Spacer(
            modifier = Modifier
                .width(8.dp)
                .height(132.dp)
                .background(color)
        )
        Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
        Column(verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall)) {
            Text(
                text = stringResource(
                    R.string.aerobic_zone_zones_for_this_workout_zone_number,
                    zoneNumber
                ), style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = zoneName,
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colors.darkGrey
            )
        }
    }
}

@Composable
private fun ZoneLimit(
    title: String,
    unit: Int?,
    zoneData: ZoneData,
    modifier: Modifier = Modifier,
    measuredZoneData: ZoneData? = null,
    onInfoClick: (() -> Unit)? = null,
) {
    Row(
        modifier = modifier
            .height(ZoneLimitHeight)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(
            modifier = Modifier
                .width(24.dp)
                .height(1.dp)
                .background(MaterialTheme.colors.cloudyGrey)
        )
        Box(
            modifier = Modifier
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colors.lightGrey,
                    shape = RoundedCornerShape(8.dp)
                )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(),
            ) {
                ZoneLimitItem(
                    title = title,
                    zoneData = zoneData,
                    unit = unit,
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxHeight(),
                )
                measuredZoneData?.let {
                    Spacer(
                        modifier = Modifier
                            .fillMaxHeight()
                            .width(1.dp)
                            .background(MaterialTheme.colors.dividerColor)
                    )
                    ZoneLimitItem(
                        title = stringResource(R.string.aerobic_zone_measured),
                        zoneData = it,
                        unit = unit,
                        onInfoClick = onInfoClick,
                        modifier = Modifier
                            .fillMaxHeight(),
                    )
                }
            }
        }
    }
}

@Composable
private fun ZoneLimitItem(
    title: String,
    unit: Int?,
    zoneData: ZoneData,
    modifier: Modifier = Modifier,
    onInfoClick: (() -> Unit)? = null,
) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Column(
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
        ) {
            Text(text = title, style = MaterialTheme.typography.bodySmall)
            Row(verticalAlignment = Alignment.Bottom) {
                Text(
                    text = stringResource(
                        R.string.aerobic_zone_zones_for_this_workout_value_unit,
                        zoneData.value,
                        unit?.let { stringResource(it) } ?: ""),
                    style = MaterialTheme.typography.bodyLargeBold,
                )
                zoneData.percentage?.let {
                    Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))
                    Text(
                        text = stringResource(
                            R.string.aerobic_zone_zones_for_this_workout_zone_percentage,
                            it
                        ),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colors.darkGrey
                    )
                }
            }
        }
        onInfoClick?.let {
            IconButton(onClick = onInfoClick) {
                Icon(
                    painter = painterResource(com.stt.android.R.drawable.ic_info_outline),
                    contentDescription = null,
                    tint = MaterialTheme.colors.primary
                )
            }
        }
    }
}

@Composable
private fun HowCanIUpdateMyZones(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(MaterialTheme.spacing.medium)
    ) {
        Text(
            text = stringResource(R.string.aerobic_zone_on_watch_you_go),
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(vertical = MaterialTheme.spacing.small)
        )
        HowCanIUpdateMyZonesItem(
            number = 1,
            iconRes = R.drawable.filter_outline,
            text = stringResource(R.string.aerobic_zone_update_my_zones_control_panel)
        )
        HowCanIUpdateMyZonesItem(
            number = 2,
            iconRes = R.drawable.settings_outline,
            text = stringResource(R.string.aerobic_zone_update_my_zones_settings)
        )
        HowCanIUpdateMyZonesItem(
            number = 3,
            iconRes = R.drawable.activity_outline,
            text = stringResource(R.string.aerobic_zone_update_my_zones_training)
        )
        HowCanIUpdateMyZonesItem(
            number = 4,
            iconRes = R.drawable.heart_rate_training_zones_outline,
            text = stringResource(R.string.aerobic_zone_update_my_zones_intensity_zones)
        )
    }
}

@Composable
private fun HowCanIUpdateMyZonesItem(
    number: Int,
    @DrawableRes iconRes: Int,
    text: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.padding(vertical = MaterialTheme.spacing.smaller),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(24.dp)
                .clip(CircleShape)
                .background(MaterialTheme.colors.nearBlack),
            contentAlignment = Alignment.Center
        ) {
            Text(text = "$number", color = Color.White)
        }
        Image(
            painter = painterResource(iconRes),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.small)
        )
        Text(
            text = text,
            style = MaterialTheme.typography.bodyLarge,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AerobicHeartRateZonesPreview() {
    AppTheme {
        AerobicHeartRateZones(
            aerobicZoneInfoUiState = fakeAerobicHeartRateZonesData,
            onCloseClick = {},
            onInfoClick = {},
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AerobicHeartRateZonesTabletPreview() {
    AppTheme {
        AerobicHeartRateZones(
            aerobicZoneInfoUiState = fakeAerobicHeartRateZonesData,
            onCloseClick = {},
            onInfoClick = {},
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AerobicPaceZonesPreview() {
    AppTheme {
        AerobicPaceZones(
            aerobicZoneInfoUiState = fakeAerobicPaceData,
            onCloseClick = {},
            onInfoClick = {},
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun AerobicPowerZonesPreview() {
    AppTheme {
        AerobicPowerZones(
            aerobicZoneInfoUiState = fakeAerobicPowerData,
            onCloseClick = {},
            onInfoClick = {},
        )
    }
}

private val fakeAerobicHeartRateZonesData = AerobicZoneInfoUiState(
    maxHr = ZoneData(
        value = "189",
        percentage = 100
    ),
    anaerobicThreshold = ZoneData(
        value = "164",
        percentage = 86
    ),
    measuredAerobicThreshold = ZoneData(
        value = "165"
    ),
    zone4Minimum = ZoneData(
        value = "155",
        percentage = 81
    ),
    aerobicThreshold = ZoneData(
        value = "146",
        percentage = 77
    ),
    measuredAnaerobicThreshold = ZoneData(
        value = "150"
    ),
    zone2Minimum = ZoneData(
        value = "136",
        percentage = 71
    ),
    unit = com.stt.android.core.R.string.TXT_BPM,
)

private val fakeAerobicPaceData = AerobicZoneInfoUiState(
    maxHr = null,
    anaerobicThreshold = ZoneData(
        value = "164",
    ),
    zone4Minimum = ZoneData(
        value = "155",
    ),
    aerobicThreshold = ZoneData(
        value = "146",
    ),
    zone2Minimum = ZoneData(
        value = "136",
    ),
    unit = com.stt.android.core.R.string.TXT_PER_KM,
)

private val fakeAerobicPowerData = AerobicZoneInfoUiState(
    maxHr = null,
    anaerobicThreshold = ZoneData(
        value = "164",
    ),
    zone4Minimum = ZoneData(
        value = "155",
    ),
    aerobicThreshold = ZoneData(
        value = "146",
    ),
    zone2Minimum = ZoneData(
        value = "136",
    ),
    unit = com.stt.android.core.R.string.TXT_W,
)
