package com.stt.android.workout.details.comments

import androidx.lifecycle.MutableLiveData
import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.failure
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutCommentController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workouts.SaveWorkoutHeaderUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.ui.components.AddCommentEditText
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.toV2
import com.stt.android.workout.details.AddCommentClickListener
import com.stt.android.workout.details.CommentsData
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.WorkoutDetailsCommentListDialogNavEvent
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workoutdetail.comments.WorkoutComment
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.awaitLast
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@ActivityRetainedScoped
class CommentsLoader
@Inject constructor(
    private val workoutCommentController: WorkoutCommentController,
    private val saveWorkoutHeaderUseCase: SaveWorkoutHeaderUseCase,
    private val currentUserController: CurrentUserController,
    private val navigationEventDispatcher: NavigationEventDispatcher,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) {
    private val commentsStateFlow: MutableStateFlow<ViewState<CommentsData>?> =
        MutableStateFlow(null)
    private lateinit var workoutHeader: WorkoutHeader

    private val _onAddCommentClick = SingleLiveEvent<Unit>()
    val onAddCommentClick: MutableLiveData<Unit> = _onAddCommentClick

    suspend fun loadComments(workoutHeader: WorkoutHeader): StateFlow<ViewState<CommentsData>?> {
        update(workoutHeader)
        return commentsStateFlow
    }

    suspend fun update(workoutHeader: WorkoutHeader) {
        this.workoutHeader = workoutHeader
        refreshComments(workoutHeader, getPreviousState().data ?: return)
    }

    private suspend fun refreshComments(
        workoutHeader: WorkoutHeader,
        initialState: CommentsData = createEmptyState(workoutHeader.key, workoutHeader.description)
    ) {
        Timber.d("Loading workout comments")
        commentsStateFlow.value = loading(initialState)
        activityRetainedCoroutineScope.launch(IO) {
            val workoutKey = workoutHeader.key

            if (workoutKey == null) {
                Timber.d("Workout hasn't been synced yet, showing empty comments state")
                commentsStateFlow.value = loaded(
                    initialState.copy(
                        formEnabled = false
                    )
                )
                return@launch
            }
            val comments: List<WorkoutComment> = runSuspendCatching {
                workoutCommentController.load(workoutKey).toV2().awaitLast()
            }.getOrElse { e ->
                Timber.w(e, "Unable to load workout comments")
                commentsStateFlow.value = failure(ErrorEvent.get(e::class), initialState)
                emptyList()
            }

            val previousEditModel = getPreviousState().data?.editModel ?: false

            commentsStateFlow.value = loaded(
                CommentsData(
                    description = workoutHeader.description,
                    commentsCount = comments.size,
                    comments = comments.takeLast(4),
                    editModel = previousEditModel,
                    workoutKey = workoutKey,
                    onTextSubmittedHandler = ::onTextSubmitted,
                    addCommentClickHandler = addCommentClickListener,
                    backKeyPressImeListener = backKeyPressImeListener,
                    viewMoreCommentClickHandler = ::navigateToCommentsDialog
                )
            )
        }
    }

    private fun navigateToCommentsDialog() {
        navigationEventDispatcher.dispatchEvent(
            WorkoutDetailsCommentListDialogNavEvent(
                workoutHeader
            )
        )
    }

    private val addCommentClickListener = object : AddCommentClickListener {
        override fun onAddCommentClicked() {
            _onAddCommentClick.postValue(Unit)
            activityRetainedCoroutineScope.launch {
                val previousState = getPreviousState().data ?: return@launch
                commentsStateFlow.value = loaded(
                    previousState.copy(
                        editModel = true
                    )
                )
            }
        }
    }

    private val backKeyPressImeListener = AddCommentEditText.BackKeyPressImeListener {
        activityRetainedCoroutineScope.launch {
            val previousState = getPreviousState().data ?: return@launch
            commentsStateFlow.value = loaded(
                previousState.copy(
                    editModel = false
                )
            )
        }
    }

    private fun createEmptyState(
        workoutKey: String?,
        description: String?
    ): CommentsData {
        return CommentsData(
            description = description,
            workoutKey = workoutKey
        )
    }

    private fun onTextSubmitted(workoutKey: String, text: String) {
        activityRetainedCoroutineScope.launch {
            coroutineScope {
                Timber.d("New comment submitted")
                val previousState = getPreviousState()

                disableCommentForm(previousState, text)

                runSuspendCatching {
                    sendComment(workoutKey, text)
                }.onFailure { e ->
                    Timber.w(e, "Failed to send new comment")
                    emitError(
                        (e as? Exception) ?: Exception(e),
                        previousState.data?.copy(
                            formEnabled = true,
                            formText = text
                        )
                    )
                    return@coroutineScope
                }

                if (workoutHeader.username == currentUserController.username) {
                    Timber.d("Updating own workout comments count")
                    saveWorkoutHeaderUseCase(workoutHeader.copy(commentCount = workoutHeader.commentCount + 1))
                }

                val enableCommentFormState = previousState.data?.copy(
                    formEnabled = true,
                    formText = "",
                    editModel = false
                )

                Timber.d("Refreshing comments data")
                if (enableCommentFormState != null) {
                    refreshComments(workoutHeader, enableCommentFormState)
                } else {
                    refreshComments(workoutHeader)
                }
            }
        }
    }

    private fun emitError(
        exception: Exception,
        previousState: CommentsData?
    ) {
        commentsStateFlow.value = failure(
            ErrorEvent.get(exception::class),
            previousState
        )
    }

    private suspend fun sendComment(workoutKey: String, text: String) = withContext(IO) {
        val currentUser = currentUserController.currentUser
        val workoutComment = WorkoutComment(
            null,
            workoutKey,
            text,
            currentUser.username,
            currentUser.realNameOrUsername,
            currentUser.profileImageUrl
        )

        workoutCommentController.sendComment(workoutComment).await()
        trackSendCommentEvent()
    }

    private suspend fun trackSendCommentEvent() = withContext(IO) {
        workoutDetailsAnalytics.trackSendCommentEvent()
    }

    private fun disableCommentForm(
        previousState: ViewState<CommentsData>,
        text: String
    ) {
        Timber.d("Disabling comment form while the comment is being sent")
        commentsStateFlow.value = loaded(
            previousState.data?.copy(
                formEnabled = false,
                formText = text
            )
        )
    }

    private fun getPreviousState(): ViewState<CommentsData> {
        return try {
            commentsStateFlow.value
        } catch (e: Exception) {
            Timber.w(
                e,
                "Error getting previous commentsData state. CommentsLoader is in wrong state."
            )
            null
        } ?: loaded(
            createEmptyState(
                workoutKey = workoutHeader.key,
                description = workoutHeader.description
            )
                .copy(
                    onTextSubmittedHandler = ::onTextSubmitted,
                    addCommentClickHandler = addCommentClickListener
                )
        )
    }
}
