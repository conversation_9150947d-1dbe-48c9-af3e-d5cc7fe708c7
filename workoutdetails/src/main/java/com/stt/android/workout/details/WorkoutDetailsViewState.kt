package com.stt.android.workout.details

import android.content.Context
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.data.CombinedData
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.google.android.gms.maps.model.LatLng
import com.stt.android.aerobiczone.AerobicZonesInfoSheet
import com.stt.android.cardlist.MapCard
import com.stt.android.common.viewstate.ViewState
import com.stt.android.core.domain.GraphType
import com.stt.android.divetrack.DiveTrack
import com.stt.android.domain.achievements.Achievement
import com.stt.android.domain.advancedlaps.LapsTableType
import com.stt.android.domain.ranking.Ranking
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.ReactionSummary
import com.stt.android.domain.user.workout.RecentWorkoutTrend
import com.stt.android.domain.user.workout.SimilarWorkoutSummary
import com.stt.android.domain.weather.WeatherConditions
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummaryData
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.domain.workouts.pictures.Picture
import com.stt.android.domain.workouts.videos.Video
import com.stt.android.infomodel.ActivitySummaryGroup
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.intensityzone.IntensityZoneLimits
import com.stt.android.laps.AutomaticLaps
import com.stt.android.laps.ManualLaps
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.maps.MapSnapshotSpec
import com.stt.android.maps.MapType
import com.stt.android.newfeed.OnOpenPremiumPromotionClicked
import com.stt.android.newfeed.OnTagClicked
import com.stt.android.ui.components.AddCommentEditText
import com.stt.android.utils.ZoneDurationData
import com.stt.android.utils.ZoneDurationUtils
import com.stt.android.utils.firstOfType
import com.stt.android.utils.orEmpty
import com.stt.android.viewmodel.sort
import com.stt.android.workout.details.charts.WorkoutLineChartData
import com.stt.android.workout.details.intensity.GraphAnalysisBottomSection
import com.stt.android.workout.details.laps.advanced.table.AdvancedLapsTableContainer
import com.stt.android.workoutdetail.comments.WorkoutComment
import com.stt.android.workoutdetail.tags.DeviceTag
import com.stt.android.workoutdetail.tags.TagsData
import com.stt.android.workoutdetail.trend.RouteSelection
import com.stt.android.workouts.details.values.WorkoutValue
import kotlin.time.Duration

typealias OnClickHandler = () -> Unit
typealias OnOpenGraphAnalysisClickHandler = (CoverImageData) -> Unit
typealias OnMuteClickHandler = suspend (videoCoverImage: CoverImage.VideoCoverImage, position: Int) -> Unit
typealias OnPageSelected = (Int) -> Unit
typealias OnAerobicZoneInfoClicked = (dest: AerobicZonesInfoSheet) -> Unit

data class WorkoutDetailsViewState(
    val workoutHeader: ViewState<WorkoutHeader?>?,
    val toolbar: ViewState<ToolbarData?>?,
    val coverImageData: ViewState<CoverImageData?>?,
    val workoutValues: ViewState<WorkoutValues?>?,
    val reactionsData: ViewState<ReactionsData?>?,
    val commentsData: ViewState<CommentsData>?,
    val shareActivityData: ViewState<ShareActivityData>?,
    val heartRateData: ViewState<HeartRateData>?,
    val workoutData: ViewState<WorkoutData?>?,
    val smlData: ViewState<Sml?>?,
    val diveExtensionData: ViewState<DiveExtension?>?,
    val diveProfileData: ViewState<DiveProfileData?>?,
    val recentTrendData: ViewState<RecentTrendData?>?,
    val competitionWorkoutSummaryData: ViewState<CompetitionWorkoutSummaryData?>?,
    val recentWorkoutSummaryData: ViewState<RecentWorkoutSummaryData?>?,
    val lapsData: ViewState<LapsData?>?,
    val hrBeltAdData: ViewState<HrBeltAdData?>?,
    val achievementsData: ViewState<AchievementsData?>?,
    val diveLocationData: ViewState<DiveLocationData?>?,
    val weatherConditions: ViewState<WeatherConditions?>?,
    val multisportPartActivity: ViewState<MultisportPartActivity?>?,
    val workoutExtensionsData: ViewState<WorkoutExtensionsData?>?,
    val onTrendPageSelected: OnPageSelected,
    val onSummaryPageSelected: OnPageSelected,
    val onTagClicked: OnTagClicked,
    val currentUsername: String,
    val isSubscribedToPremium: Boolean,
    val onOpenPremiumPromotionClicked: OnOpenPremiumPromotionClicked,
    val zoneAnalysisData: ViewState<ZoneAnalysisData?>?,
    val diveTrackData: ViewState<DiveTrackData?>?,
    val onAerobicZoneInfoClicked: OnAerobicZoneInfoClicked,
    val hideBarInfo: Boolean,
) {
    val showPagerIndicator: Boolean =
        coverImageData?.data?.coverImages?.let { it.size > 1 } ?: false

    val tagsData: TagsData?
        get() {
            val workoutHeader = workoutHeader?.data
            val summaryExtension: SummaryExtension? =
                workoutExtensionsData?.data?.workoutExtensions?.firstOfType()
            if (summaryExtension == null && workoutHeader == null) return null

            return TagsData(
                deviceTag = DeviceTag.createOrNull(
                    displayName = summaryExtension?.displayName,
                    productType = summaryExtension?.productType
                ),
                suuntoTags = workoutHeader?.suuntoTags.sort(),
                userTags = workoutHeader?.userTags.sort(),
                isOwnWorkout = workoutHeader?.username == currentUsername,
            )
        }
}

data class CoverImageData(
    val activityType: ActivityType,
    val showRouteView: Boolean,
    val routePoints: List<LatLng>,
    val totalDistance: Double,
    val showWorkoutPlaybackButton: Boolean,
    val supportsWorkoutPlaybackOnMapView: Boolean,
    val isShareable: Boolean,
    val onShareClicked: OnShareClickHandler,
    val onRouteViewClick: OnClickHandler,
    val onOpenGraphAnalysisClick: OnOpenGraphAnalysisClickHandler,
    val coverImages: List<CoverImage>,
    val mapType: MapType,
    val multisportPartActivity: MultisportPartActivity? = null
)

typealias OnShareClickHandler = (Int) -> Unit

sealed class CoverImage {
    abstract val onClickHandler: OnClickHandler?
    val clickable: Boolean by lazy { onClickHandler != null }
    val showPlayButton: Boolean by lazy { this is VideoCoverImage }

    @Suppress("RedundantOverride")
    override fun equals(other: Any?): Boolean {
        // Avoid Epoxy Processor Exception: Attribute does not implement hashCode
        return super.equals(other)
    }

    @Suppress("RedundantOverride")
    override fun hashCode(): Int {
        // Avoid Epoxy Processor Exception: Attribute does not implement hashCode
        return super.hashCode()
    }

    data class DefaultCoverImage(
        @DrawableRes val activityCoverPicture: Int,
        override val onClickHandler: OnClickHandler? = null
    ) : CoverImage()

    data class PhotoCoverImage(
        val picture: Picture,
        override val onClickHandler: OnClickHandler
    ) : CoverImage()

    data class VideoCoverImage(
        val video: Video,
        val isMuted: Boolean,
        val userAgent: String,
        val onMuteClickHandler: OnMuteClickHandler,
        override val onClickHandler: OnClickHandler
    ) : CoverImage()

    data class RouteCoverImage(
        val workoutHeaderId: Int = 0,
        val mapCard: MapCard,
        val activityType: Int = 0,
        val runsOrLifts: List<List<LatLng>> = emptyList(),
        override val onClickHandler: OnClickHandler,
        var colorfulPolylines: MapSnapshotSpec.ColorfulPolylines? = null,
    ) : CoverImage()
}

data class ToolbarData(
    val name: String,
    val workoutStartTime: Long,
    val sharingFlags: Int,
    val hasRoute: Boolean,
    val isOwnWorkout: Boolean,
    val isFieldTester: Boolean,
    val locationPlaceName: String?,
    val onTitleClicked: OnClickHandler,
    val onMenuItemClicked: (Int) -> Boolean,
    val showPremiumRequiredNotes: Boolean
)

data class WorkoutValues(
    val workoutValuesContainer: WorkoutValuesContainer,
    val onValueSelectedListener: WorkoutValueClickListener,
    // Multisport: Workout values from activity windows. One per sport.
    val multisportValuesContainers: List<WorkoutValuesContainer> = emptyList(),
    val onMultisportDetailsClicked: MultisportDetailsClickHandler? = null,
    val onViewMoreClicked: () -> Unit
)

data class SuuntoPlusGroup(
    val name: String,
    val workoutValues: List<WorkoutValue>,
)

typealias MultisportDetailsClickHandler = (MultisportPartActivity) -> Unit

data class WorkoutValuesContainer(
    val activityType: Int,
    val multisportPartActivity: MultisportPartActivity?,
    val workoutValues: List<WorkoutValue>,
    val summaryGroups: List<ActivitySummaryGroup> = emptyList(),
    val suuntoPlusGroups: List<SuuntoPlusGroup> = emptyList(),
)

interface WorkoutValueClickListener {
    fun onWorkoutValueClicked(workoutValue: WorkoutValue)
}

data class ReactionsData(
    val workoutId: Int,
    val reactionSummary: ReactionSummary? = null,
    val userAvatarsUrls: List<String?> = emptyList(),
    val likeClickHandler: LikeClickHandler? = null,
    val avatarClickHandler: AvatarClickHandler? = null
)

typealias AvatarClickHandler = (String) -> Unit
typealias LikeClickHandler = (Int, ReactionSummary) -> Unit

data class CommentsData(
    val commentsCount: Int = 0,
    val description: String? = null,
    val comments: List<WorkoutComment> = emptyList(),
    val editModel: Boolean = false,
    val onTextSubmittedHandler: OnTextSubmittedHandler? = null,
    val formEnabled: Boolean = true,
    val formText: String = "",
    val workoutKey: String? = null,
    val addCommentClickHandler: AddCommentClickListener? = null,
    val backKeyPressImeListener: AddCommentEditText.BackKeyPressImeListener? = null,
    val viewMoreCommentClickHandler: OnClickHandler? = null
)

interface AddCommentClickListener {
    fun onAddCommentClicked()
}

typealias OnTextSubmittedHandler = (workoutKey: String, text: String) -> Unit

data class ShareActivityData(
    val sharingFlags: Int,
    val shareActivityClickHandler: ShareActivityClickHandler,
    val isOwnWorkout: Boolean
)

typealias ShareActivityClickHandler = (sharingFlag: Int) -> Unit

data class HeartRateData(
    val graphData: HrGraphData?,
    val multisportPartGraphData: Map<MultisportPartActivity, HrGraphData>,
    val hrGraphClickHandler: HeartRateGraphClickHandler,
    val viewOnMapClickHandler: ViewOnMapClickHandler
)

typealias HeartRateGraphClickHandler = () -> Unit
typealias ViewOnMapClickHandler = () -> Unit

data class AerobicIqGraphData(
    val entries: List<Entry>,
    val zoneLimits: IntensityZoneLimits,
    val vo2Max: ZoneDurationData?,
    val anaerobic: ZoneDurationData?,
    val aerobic: ZoneDurationData?,
    val anaerobicHrThreshold: Double?,
    val aerobicHrThreshold: Double?,
    val baseline: Double?,
    val cumulativeBaseline: Double?,
) {
    fun hasValidData(): Boolean = entries.isNotEmpty()
}

data class HrGraphData(
    val avgHr: Int,
    val entries: List<Entry>,
    val zoneLimits: IntensityZoneLimits,
    val thresholds: List<Float>,
    val durations: List<Long>,
    val showViewOnMap: Boolean
) {
    //region Overriding equals/hashCode since LineData does not provide them.
    private val hashCode: Int = createHashCode()

    override fun equals(other: Any?): Boolean = hashCode == other.hashCode()

    override fun hashCode(): Int {
        return hashCode
    }

    private fun createHashCode(): Int {
        var result = avgHr
        result = 31 * result + zoneLimits.hashCode()
        result = 31 * result + thresholds.hashCode()
        result = 31 * result + durations.hashCode()
        result = 31 * result + entries.size.hashCode()
        return result
    }
    //endregion

    fun hasValidData(): Boolean = entries.isNotEmpty()
}

data class WorkoutAnalysisData(
    @StringRes val buttonStringRes: Int,
    val pagerData: WorkoutAnalysisPagerData,
    val onAnalysisTapped: OnWorkoutAnalysisTapped,
    val showViewOnMap: Boolean,
    val multisportPartAnalysisData: Map<MultisportPartActivity, WorkoutAnalysisData> = emptyMap()
)

data class WorkoutAnalysisPagerData(
    val workoutHeader: WorkoutHeader,
    val graphData: List<WorkoutLineChartData>,
    val workoutData: WorkoutData?,
    val sml: Sml?,
    val diveExtension: DiveExtension?,
    val onGraphTapped: OnWorkoutAnalysisGraphTapped,
    val multisportPartActivity: MultisportPartActivity? = null
)

typealias OnWorkoutAnalysisTapped = (GraphType) -> Unit
typealias OnWorkoutAnalysisGraphTapped = (GraphType, MultisportPartActivity?) -> Unit

data class DiveProfileData(
    val sml: Sml?,
    val diveExtension: DiveExtension?,
    val onDiveProfileTapped: OnDiveProfileTapped,
    val onShowEventsTapped: OnShowEventsTapped,
    val graphData: WorkoutLineChartData? = null
)

typealias OnDiveProfileTapped = () -> Unit
typealias OnShowEventsTapped = () -> Unit

data class RecentTrendData(
    val recentWorkoutTrend: RecentWorkoutTrendNew,
    val routeSelection: RouteSelection,
    val hideSelectionSpinner: Boolean,
    val currentPage: Int,
    val onRouteSelection: OnRouteSelection,
    val onCompareClicked: OnCompareClicked
)

typealias OnCompareClicked = (RecentWorkoutTrendNew, RouteSelection) -> Unit
typealias OnRouteSelection = (RouteSelection, Int) -> Unit

data class RecentWorkoutTrendNew(
    val currentWorkout: WorkoutHeader,
    val previousWorkout: WorkoutHeader?,
    val bestWorkoutOnSimilarRoute: WorkoutHeader?,
    val previousWorkoutOnSimilarRoute: WorkoutHeader?,
    val rankingOfSimilarDistance: Int,
    val rankingOnSimilarRoute: Int,
    val durationEntries: List<Entry>?,
    val distanceEntries: List<Entry>?,
    val speedEntries: List<Entry>?,
    val paceEntries: List<Entry>?,
    val energyEntries: List<Entry>?,
    val averageHeartRateEntries: List<Entry>?,
    val averageCadenceEntries: List<Entry>?,
    @ColorRes private val dataSetColorRes: Int,
    @ColorRes private val dataColorResList: List<Int>,
) {
    private fun getDurationData(context: Context): LineData? = getLineData(durationEntries, context)
    private fun getDistanceData(context: Context): LineData? = getLineData(distanceEntries, context)
    private fun getSpeedData(context: Context): LineData? = getLineData(speedEntries, context)
    private fun getPaceData(context: Context): LineData? = getLineData(paceEntries, context)
    private fun getEnergyData(context: Context): LineData? = getLineData(energyEntries, context)
    private fun getAverageHeartRateData(context: Context): LineData? =
        getLineData(averageHeartRateEntries, context)

    private fun getAverageCadenceData(context: Context): LineData? =
        getLineData(averageCadenceEntries, context)

    private fun getLineData(entries: List<Entry>?, context: Context): LineData? {
        if (entries == null) return null

        val dataSetColor = ContextCompat.getColor(context, dataSetColorRes)
        val dataColorList = dataColorResList.map { ContextCompat.getColor(context, it) }

        val lineData = LineData()
        val lineDataSet = LineDataSet(entries, "")
        lineDataSet.lineWidth = 1.5f
        lineDataSet.colors = dataColorList
        lineDataSet.setDrawFilled(true)
        lineDataSet.fillColor = dataSetColor
        lineDataSet.fillAlpha = 64
        lineDataSet.setDrawValues(false)
        lineDataSet.setDrawHighlightIndicators(false)
        lineDataSet.setDrawCircles(true)
        lineDataSet.setDrawCircleHole(false)
        lineDataSet.circleColors = dataColorList
        lineDataSet.circleRadius = 3f
        lineData.addDataSet(lineDataSet)
        return lineData
    }

    fun toOldModel(context: Context): RecentWorkoutTrend {
        return RecentWorkoutTrend(
            currentWorkout,
            previousWorkout,
            getDurationData(context),
            getDistanceData(context),
            getSpeedData(context),
            getPaceData(context),
            getEnergyData(context),
            getAverageHeartRateData(context),
            getAverageCadenceData(context)
        )
    }

    //region Overriding equals/hashCode since LineData does not provide them.
    private val hashCode: Int = createHashCode()

    override fun equals(other: Any?): Boolean = hashCode == other.hashCode()

    override fun hashCode(): Int {
        return hashCode
    }

    private fun createHashCode(): Int {
        var result = currentWorkout.hashCode()
        result = 31 * result + (previousWorkout?.hashCode() ?: 0)
        durationEntries?.let { result = 31 * result + it.size }
        distanceEntries?.let { result = 31 * result + it.size }
        paceEntries?.let { result = 31 * result + it.size }
        energyEntries?.let { result = 31 * result + it.size }
        averageHeartRateEntries?.let { result = 31 * result + it.size }
        averageCadenceEntries?.let { result = 31 * result + it.size }
        return result
    }
    //endregion
}

data class AdvancedLapsData(
    val stId: Int,
    val container: AdvancedLapsTableContainer,
    val pageChangeListener: LapPageChangeListener
)

data class LapsData(
    val manualLaps: ManualLaps?,
    val automaticLaps: AutomaticLaps?
) {
    val hasLaps: Boolean
        get() = hasManualLaps || hasAutomaticLaps

    val hasManualLaps: Boolean
        get() = manualLaps?.completeLaps?.size?.run { this > 1 } ?: false

    private val hasAutomaticLaps: Boolean
        get() = automaticLaps != null
}

interface LapPageChangeListener {
    suspend fun onPageChanged(
        lapsTableType: LapsTableType,
        activityName: String
    )
}

data class RecentWorkoutSummaryData(
    val summary: RecentWorkoutSummary,
    val currentSummaryPage: Int,
    val onViewMoreClicked: OnSummaryViewMoreClicked,
)

typealias OnSummaryViewMoreClicked = () -> Unit

data class WorkoutExtensionsData(
    val workoutExtensions: List<WorkoutExtension>,
)

data class RecentWorkoutSummaryLineData(
    val summary: RecentWorkoutSummary,
    val duration: CombinedData,
    val distance: CombinedData,
    val speed: CombinedData,
    val pace: CombinedData,
    val energy: CombinedData,
    val averageHeartRate: CombinedData,
    val averageCadence: CombinedData,
    val highLightIndex: Int
) {
    fun toOldModel(): com.stt.android.domain.user.workout.RecentWorkoutSummary {
        return com.stt.android.domain.user.workout.RecentWorkoutSummary(
            summary.toOldModel(),
            duration,
            distance,
            speed,
            pace,
            energy,
            averageHeartRate,
            averageCadence,
            highLightIndex
        )
    }
}

data class RecentWorkoutSummary(
    val referenceWorkout: WorkoutHeader,
    val startTime: Long,
    val endTime: Long,
    val workouts: Int,
    val workoutsChange: Int,
    val distance: Double,
    val distanceChange: Int,
    val energy: Double,
    val energyChange: Int,
    val duration: Long,
    val durationChange: Int
) {
    fun toOldModel(): com.stt.android.domain.user.workout.RecentWorkoutSummary.Summary =
        com.stt.android.domain.user.workout.RecentWorkoutSummary.Summary(
            referenceWorkout,
            startTime,
            endTime,
            workouts,
            workoutsChange,
            distance,
            distanceChange,
            energy,
            energyChange,
            duration,
            durationChange
        )
}

data class HrBeltAdData(
    val isVisible: Boolean,
)

data class AchievementsData(
    val isCurrentUserWorkout: Boolean,
    val oldFollowerRankings: List<Ranking> = emptyList(),
    val achievement: Achievement? = null,
    val similarWorkoutSummary: SimilarWorkoutSummary? = null
)

data class DiveLocationData(
    val isUnconfirmed: Boolean,
    val latLng: LatLng?,
    val onClick: OnClickHandler,
)

data class ZoneAnalysisGraphType(
    val graphType: GraphType,
    val graphTitle: String,
    val graphUnit: String
) {
    val isAerobicIqGraph
        get() = graphType is GraphType.Summary && (graphType.summaryGraph in listOf(
            SummaryGraph.AEROBICZONE,
            SummaryGraph.AEROBICHRTHRESHOLDS,
            SummaryGraph.AEROBICPOWERTHRESHOLDS
        ))

    companion object {
        val EMPTY = ZoneAnalysisGraphType(
            graphType = GraphType.NONE,
            graphTitle = "",
            graphUnit = ""
        )
    }
}

data class ZoneAnalysisData(
    val mainGraphData: ZoneAnalysisChartData?,
    val secondaryGraphData: ZoneAnalysisChartData?,
    val mainGraphZoneLimits: IntensityZoneLimits?,
    val mainGraphDurations: List<Duration>?,
    val availableGraphTypes: Set<GraphType>,
    val isSwimming: Boolean,
    val multisportPart: MultisportPartActivity?,
    val infoModelFormatter: InfoModelFormatter,
    val onGraphTypeSelected: (graphType:GraphType, yAxis: ZonedAnalysisYAxisType) -> Unit,
    val onSwitchClicked: (mainGraphType : GraphType, secondaryGraphType: GraphType) -> Unit,
    val onFullScreenAnalysisClicked: (graphType: GraphType, multisportPart: MultisportPartActivity?) -> Unit,
    val vo2Max : ZoneDurationData?,
    val anaerobic : ZoneDurationData?,
    val aerobic : ZoneDurationData?,
) {
    val graphType = mainGraphData?.lineChartData?.graphType
    val graphAnalysisBottomSection = when (graphType) {
        GraphType.Summary(SummaryGraph.HEARTRATE),
        GraphType.Summary(SummaryGraph.PACE),
        GraphType.Summary(SummaryGraph.POWER) -> GraphAnalysisBottomSection.HeartRate(
            title = when (graphType) {
                GraphType.Summary(SummaryGraph.HEARTRATE) -> R.string.zone_duration_title_heart_rate
                GraphType.Summary(SummaryGraph.PACE) -> R.string.zone_duration_title_pace
                GraphType.Summary(SummaryGraph.POWER) -> R.string.zone_duration_title_power
                else -> throw IllegalArgumentException("Invalid SummaryGraph type for HeartRate section")
            },
            zoneDurationData = mainGraphDurations?.let {
                ZoneDurationUtils.createZoneDurations(it, infoModelFormatter)
            } ?: emptyList()
        )

        GraphType.Summary(SummaryGraph.AEROBICZONE) -> GraphAnalysisBottomSection.AerobicZone(
            vo2Max = vo2Max.orEmpty(),
            anaerobic = anaerobic.orEmpty(),
            aerobic = aerobic.orEmpty()
        )

        GraphType.Summary(SummaryGraph.GASCONSUMPTION),
        GraphType.Summary(SummaryGraph.TANKPRESSURE) -> {
            GraphAnalysisBottomSection.DiveGases(
                diveGases = mainGraphData?.lineChartData?.data?.mapNotNull { workoutLineEntry ->
                    workoutLineEntry.lineColor?.let { color ->
                        AnalysisDiveGasData(name = workoutLineEntry.name, color = color)
                    }
                }.orEmpty()
            )
        }

        else -> null
    }
}

data class ZoneAnalysisChartData(
    val lineChartData: WorkoutLineChartData,
    val diveGases: List<AnalysisDiveGasData> = emptyList(),
    val dataMinXValue: Float,
    val dataMaxXValue: Float,
    val dataMinYValue: Float,
    val dataMaxYValue: Float,
)

data class AnalysisDiveGasData(val name: String, val color: Int)

enum class ZonedAnalysisYAxisType{
    MAIN,
    SECONDARY
}

data class DiveTrackData(
    val diveTrack: DiveTrack,
    val onShowFullscreenClicked: () -> Unit
)
