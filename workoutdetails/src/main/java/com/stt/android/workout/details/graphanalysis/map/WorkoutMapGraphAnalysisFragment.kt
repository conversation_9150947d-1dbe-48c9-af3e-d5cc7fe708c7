package com.stt.android.workout.details.graphanalysis.map

import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.core.os.BundleCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.doOnLayout
import androidx.core.view.isVisible
import androidx.core.view.marginTop
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.commitNow
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.model.LatLng
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.snackbar.Snackbar
import com.stt.android.analytics.AnalyticsEvent.POPULAR_ROUTES_SCREEN
import com.stt.android.analytics.AnalyticsEvent.WORKOUT_ANALYSIS_SCREEN
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.Map3dModeInputMethod.TILTING_WITH_FINGERS
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutAnalysisScreenSource.MAP_ANALYSIS_OPEN_FULLSCREEN_BUTTON
import com.stt.android.common.ui.avalanchemap.AvalancheInfoHelper
import com.stt.android.common.ui.avalanchemap.AvalancheInfoPopupFragment
import com.stt.android.common.ui.avalanchemap.AvalancheLegend
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.core.domain.GraphType
import com.stt.android.di.Forced2dPlaybackMode
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.home.explore.ExploreAnalytics
import com.stt.android.maps.MapFloatingActionButtons
import com.stt.android.maps.MapType
import com.stt.android.maps.SuuntoFreeCameraUpdate
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.isAvalancheMap
import com.stt.android.maps.newLatLng
import com.stt.android.models.MapSelectionModel
import com.stt.android.premium.PremiumMapFeaturesAccessHandler
import com.stt.android.premium.PremiumPromotionNavigator
import com.stt.android.ui.map.Map3dEnabledLiveData
import com.stt.android.ui.map.SelectedMapTypeLiveData
import com.stt.android.ui.map.SuuntoScaleBarDefaultOptionsFactory
import com.stt.android.ui.map.mapoptions.MapOptionsFragment
import com.stt.android.ui.map.selection.MapSelectionDialogFragment
import com.stt.android.workout.details.R
import com.stt.android.workout.details.WorkoutDetailsConstants.RESULT_UPDATED_GRAPH_ANALYSIS_SELECTIONS
import com.stt.android.workout.details.WorkoutDetailsViewModelNew
import com.stt.android.workout.details.databinding.WorkoutMapGraphAnalysisFragmentBinding
import com.stt.android.workout.details.graphanalysis.GraphAnalysisFragment
import com.stt.android.workout.details.graphanalysis.GraphAnalysisSelections
import com.stt.android.workout.details.graphanalysis.fullscreen.FullscreenGraphAnalysisActivity
import com.stt.android.workout.details.graphanalysis.playback.Workout2DPlaybackCameraConfig
import com.stt.android.workout.details.graphanalysis.playback.Workout3DPlaybackCameraConfig
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackPauseReason
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.R as BaseR

@FlowPreview
@AndroidEntryPoint
class WorkoutMapGraphAnalysisFragment :
    Fragment(),
    GraphAnalysisFragment.Listener {
    @Inject
    internal lateinit var scaleBarOptionsFactory: SuuntoScaleBarDefaultOptionsFactory

    @Inject
    internal lateinit var mapSelectionModel: MapSelectionModel

    @Inject
    internal lateinit var selectedMapTypeLiveData: SelectedMapTypeLiveData

    @Inject
    internal lateinit var map3dEnabledLiveData: Map3dEnabledLiveData

    @Inject
    internal lateinit var exploreAnalytics: ExploreAnalytics

    @Inject
    lateinit var avalancheInfoHelper: AvalancheInfoHelper

    @Inject
    lateinit var premiumMapFeaturesAccessHandler: PremiumMapFeaturesAccessHandler

    @Inject
    lateinit var premiumPromotionNavigator: PremiumPromotionNavigator

    @JvmField
    @Inject
    @Forced2dPlaybackMode
    internal var forced2dPlaybackMode: Boolean = false

    private lateinit var binding: WorkoutMapGraphAnalysisFragmentBinding
    private lateinit var mapView: GraphAnalysisWorkoutMapView
    private var bottomSheetBehavior: BottomSheetBehavior<NestedScrollView>? = null
    private var shouldAutoOpenBottomSheetOnGraphReady = true
    private var showMapSuccessfullyCalledForView = false

    private val sharedViewModel: WorkoutDetailsViewModelNew by activityViewModels()

    private val viewModel: WorkoutMapGraphAnalysisViewModel by viewModels()

    private val mainGraphTypeGraphTypeChangeViewModel: MainGraphTypeChangeViewModel by activityViewModels()

    private val highlightMovedInFullscreenListener =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            val updatedSelections = result.data?.extras?.getParcelable<GraphAnalysisSelections>(
                RESULT_UPDATED_GRAPH_ANALYSIS_SELECTIONS
            )
            val graphAnalysisFragment =
                childFragmentManager.findFragmentById(R.id.graph_analysis_fragment_container) as? GraphAnalysisFragment

            if (updatedSelections != null && graphAnalysisFragment != null) {
                graphAnalysisFragment.setGraphAnalysisSelections(updatedSelections)
            }
        }

    private val onMap3dModeChangedWithTiltListener = SuuntoMap.OnMap3dModeChangedListener { enabled ->
        mapSelectionModel.map3dEnabled = enabled
        exploreAnalytics.trackMap3dModeChange(POPULAR_ROUTES_SCREEN, TILTING_WITH_FINGERS)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        sharedViewModel.showMultisportPartActivity(arguments?.get("multisportPartActivity") as? MultisportPartActivity)

        if (savedInstanceState != null) {
            shouldAutoOpenBottomSheetOnGraphReady = savedInstanceState.getBoolean(
                KEY_SHOULD_AUTO_OPEN_SHEET,
                shouldAutoOpenBottomSheetOnGraphReady
            )
        }

        premiumMapFeaturesAccessHandler.onCreate(
            this,
            AnalyticsPropertyValue.BuyPremiumPopupShownSource.WORKOUT_MAP_ANALYSIS
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = WorkoutMapGraphAnalysisFragmentBinding.inflate(inflater, container, false)
        val mapOptions = SuuntoMapOptions(
            mapType = mapSelectionModel.selectedMapType.name,
            map3dMode = mapSelectionModel.map3dEnabled,
            enable3dLocation = true,
            showMyLocationMarker = false,
        )

        if (savedInstanceState == null) {
            childFragmentManager.commitNow {
                val initialMainGraphType = arguments?.let {
                    BundleCompat.getParcelable(it, ARG_INITIAL_MAIN_GRAPH_TYPE, GraphType::class.java)
                }
                add(
                    R.id.graph_analysis_fragment_container,
                    GraphAnalysisFragment::class.java,
                    GraphAnalysisFragment.createArguments(
                        displayMode = GraphAnalysisFragment.DisplayMode.MINIMAL,
                        initialMainGraphType = initialMainGraphType,
                    ),
                )
            }
        }

        mapView = GraphAnalysisWorkoutMapView(inflater.context, mapOptions, null)
        // WorkoutMapView's state restoration requires that an ID is set. Having duplicate ID in the
        // hierarchy is allowed and doesn't break getViewById, so just use the container ID
        mapView.id = binding.graphAnalysisWorkoutMapViewContainer.id
        binding.graphAnalysisWorkoutMapViewContainer.addView(mapView)

        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        // Make map invisible until the camera gets moved to show the workout to prevent the zoomed
        // out world map showing for few frames. Google Maps don't send the camera idle event we
        // use to stop hiding the map if the view is invisible, so we need to use a separate hiding
        // view instead of setting the map itself to invisible
        binding.hideMapLoadingView.visibility = View.VISIBLE

        binding.avalancheInfo.setContent {
            AvalancheLegend(onClick = {
                AvalancheInfoPopupFragment.newInstance().show(childFragmentManager, null)
            })
        }

        BottomSheetBehavior.from(binding.bottomSheetGraphAnalysis).isFitToContents = false

        initMap(savedInstanceState)
        setInitialSkiMapIfNeeded()
        initLiveData()

        premiumMapFeaturesAccessHandler.startCheckingForPremiumAccess(
            viewLifecycleOwner,
            binding.root as ViewGroup
        )
        binding.mapFloatingActionButtonLayout.setContentWithTheme {
            val viewState = viewModel.mapFloatingActionButtonState.collectAsState().value
            MapFloatingActionButtons(
                modifier = Modifier.padding(MaterialTheme.spacing.medium),
                showInfo = viewState.showInfo,
                showLocation = viewState.showLocation,
                showSearch = viewState.showSearch,
                showMapLayers = viewState.showMapLayers,
                show3D = viewState.show3D,
                enable3D = viewState.enable3D,
                button3DEnabled = viewState.button3DEnabled,
                locationEnabled = viewState.locationEnabled,
                on3DButtonClick = {
                    viewModel.handle3dOptionToggled()
                },
                onMapLayersButtonClick = {
                    MapSelectionDialogFragment.newInstance(
                        mapsProviderName = mapView.mapsProviderName,
                        showHeatmaps = false,
                        showRoadSurface = false,
                        showMyTracks = false,
                        showMyPOIsGroup = false,
                        mapCenter = mapView.cameraCenter,
                        analyticsSource = WORKOUT_ANALYSIS_SCREEN
                    )
                        .show(childFragmentManager, MapSelectionDialogFragment.FRAGMENT_TAG)
                },
            )
        }
    }

    private fun setInitialSkiMapIfNeeded() {
        // value is in arguments when analysis fragment is opened from feed
        if (arguments?.getBoolean(WorkoutMapGraphAnalysisViewModel.ARG_FORCE_SKI_MAP) == true || sharedViewModel.shouldForceSkiMap) {
            viewModel.forceSkiMap()
        }
    }

    // Check for bottom sheet's initial state requires view state to be restored, rest of code
    // is here as its simpler to have them in same block as the BottomSheetCallback creation
    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)

        // when screen orientation changed, colorful tracks will be removed, so need set updateLapTrack to false
        // to redraw colorful tracks and selected lap track
        viewModel.updateWorkoutMapAnalysisData()

        val graphAnalysisFragment =
            childFragmentManager.findFragmentById(R.id.graph_analysis_fragment_container) as GraphAnalysisFragment
        graphAnalysisFragment.listener = this@WorkoutMapGraphAnalysisFragment

        val bottomSheetBehavior = BottomSheetBehavior.from(binding.bottomSheetGraphAnalysis)
        this.bottomSheetBehavior = bottomSheetBehavior
        bottomSheetBehavior.addBottomSheetCallback(object :
            BottomSheetBehavior.BottomSheetCallback() {
            var sheetHalfExpanded =
                bottomSheetBehavior.state == BottomSheetBehavior.STATE_HALF_EXPANDED

            override fun onSlide(bottomSheet: View, slideOffset: Float) {
                if (!viewModel.isPlaybackResumed) {
                    updateMapBottomPadding()
                }
            }

            override fun onStateChanged(bottomSheet: View, newState: Int) {
                if (viewModel.isPlaybackResumed) {
                    updateMapBottomPadding()
                }
                if (sheetHalfExpanded) {
                    if (newState == BottomSheetBehavior.STATE_EXPANDED || newState == BottomSheetBehavior.STATE_COLLAPSED) {
                        viewModel.onBottomSheetOpened()
                        sheetHalfExpanded = false
                    }
                } else {
                    if (newState == BottomSheetBehavior.STATE_HALF_EXPANDED) {
                        viewModel.onBottomSheetClosed()
                        sheetHalfExpanded = true
                    }
                }
            }
        })

        binding.root.doOnLayout { root ->
            // If gesture navigation is on, bump the peek height to make it less likely for
            // attempts to open the bottom sheet to trigger app switching gesture
            val halfExpandedProportionalHeight = TypedValue().run {
                resources.getValue(
                    R.fraction.graph_analysis_bottomsheet_half_expanded_height_fraction,
                    this,
                    true
                )
                root.height * float
            }
            val halfExpandedMinimumHeight =
                resources.getDimension(R.dimen.graph_analysis_bottom_sheet_half_expanded_min_height)
            val initiallyVisibleContentHeight = maxOf(halfExpandedProportionalHeight, halfExpandedMinimumHeight)
                .roundToInt()
                .coerceAtMost(binding.root.height - binding.graphAnalysisFragmentContainer.marginTop)

            graphAnalysisFragment.setInitiallyVisibleContentHeight(initiallyVisibleContentHeight)

            val halfExpandedExtra =
                resources.getDimension(R.dimen.graph_analysis_bottom_sheet_half_expanded_extra)
            val halfExpandedTop =
                initiallyVisibleContentHeight + binding.graphAnalysisFragmentContainer.top + halfExpandedExtra
            // The ratio could be exactly 1 on very small screens or when using split screen
            // (shouldn't be more than 1 because of the coerceAtMost call when calculating initiallyVisibleContentHeight),
            // so clamp it to allowed value range of 0-1 non-inclusive
            bottomSheetBehavior.halfExpandedRatio =
                (halfExpandedTop / binding.root.height.toFloat()).coerceIn(0.1f, 0.99f)

            fun updateExpandedOffset() {
                bottomSheetBehavior.expandedOffset = maxOf(
                    binding.root.height - binding.bottomSheetGraphAnalysisContent.height,
                    0
                )
            }

            updateExpandedOffset()
            updateBottomSheetBehaviorPeekHeight()
            binding.bottomSheetGraphAnalysisContent.addOnLayoutChangeListener { _, _, _, _, _, _, _, _, _ ->
                updateExpandedOffset()
                updateBottomSheetBehaviorPeekHeight()
                if (!viewModel.isPlaybackResumed) {
                    updateMapBottomPadding()
                }
            }

            updateMapBottomPadding()
            updateAspectRatio()
        }
    }

    override fun onStart() {
        super.onStart()
        mapView.onStart()
    }

    override fun onResume() {
        super.onResume()
        mapView.onResume()
    }

    override fun onPause() {
        super.onPause()
        viewModel.pausePlayback(WorkoutPlaybackPauseReason.ScreenExit)
        mapView.onPause()
    }

    override fun onStop() {
        super.onStop()
        mapView.storeCamera()
        mapView.onStop()
    }

    override fun onDestroyView() {
        showMapSuccessfullyCalledForView = false
        mapSelectionModel.selectedMapType = viewModel.userSelectedMapType
        // TP #118433
        // Mapbox recreates the map on orientation change with the same camera as before,
        // so even if we tell it to recreate with 2D mode the camera tilt will immediately change
        // it back to 3D, which would confuse our tracking of user selection map settings.
        // Revert the forced 3D only when exiting the screen.
        if (activity?.isChangingConfigurations != true) {
            mapSelectionModel.map3dEnabled = viewModel.userSelectedMap3dEnabled
        }
        activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        mapView.onDestroy()
        super.onDestroyView()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mapView.onSaveInstanceState(outState)
        outState.putBoolean(KEY_SHOULD_AUTO_OPEN_SHEET, shouldAutoOpenBottomSheetOnGraphReady)
    }

    override fun onLowMemory() {
        super.onLowMemory()
        mapView.onLowMemory()
    }

    override fun onDestroy() {
        super.onDestroy()
        premiumMapFeaturesAccessHandler.onDestroy(activity)
    }

    private fun initMap(savedInstanceState: Bundle?) {
        with(mapView) {
            onCreate(savedInstanceState)
            showScaleBar(scaleBarOptionsFactory)
            setAllGesturesEnabled(true)
        }
    }

    private fun initLiveData() {
        viewModel.playbackEnabled.observeNotNull(viewLifecycleOwner) { (enabled, disableMapInteractionsDuringPlayback) ->
            mapView.setMapClicksDisabled(enabled && disableMapInteractionsDuringPlayback)
            if (!forced2dPlaybackMode && disableMapInteractionsDuringPlayback) {
                val fragment = childFragmentManager
                    .findFragmentByTag(MapOptionsFragment.TAG) as? MapOptionsFragment
                fragment?.set3dMapOptionViewEnabled(!enabled)
            }

            if (enabled) {
                activity?.window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            } else {
                activity?.window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            }
        }

        viewModel.loadingPlaybackData.observeNotNull(viewLifecycleOwner) {
            binding.loadingPlaybackDataSpinner.isVisible = it
        }

        viewModel.supportsPlayback.observeNotNull(viewLifecycleOwner) {
            binding.bottomSheetGraphAnalysis.isVisible = it
            updateMapBottomPadding()
        }

        viewModel.cameraConfig.observeNotNull(viewLifecycleOwner) { cameraConfig ->
            when (cameraConfig) {
                is Workout3DPlaybackCameraConfig -> update3d(cameraConfig)
                is Workout2DPlaybackCameraConfig -> update2d(cameraConfig)
            }
        }

        viewModel.closeBottomSheetEvent.observeK(viewLifecycleOwner) {
            shouldAutoOpenBottomSheetOnGraphReady = false
            BottomSheetBehavior.from(binding.bottomSheetGraphAnalysis).state =
                BottomSheetBehavior.STATE_HALF_EXPANDED
        }

        viewModel.workoutMapRouteData.observeNotNull(viewLifecycleOwner) { routeData ->
            routeData?.let {
                showMap(it)
            }
        }

        viewModel.workoutColorfulTrackMapData.observeNotNull(viewLifecycleOwner) { routeData ->
            routeData ?: return@observeNotNull

            try {
                mapView.showColorfulTrackWorkout(routeData)
                showMapSuccessfullyCalled()
            } catch (t: Throwable) {
                Timber.w(t, "Showing workout map failed")
                binding.hideMapLoadingView.visibility = View.GONE
                viewModel.onMapReadyForPlayback()
            }
        }

        var errorSnackbar: Snackbar? = null
        viewModel.showLoadingErrorMessage.observeNotNull(viewLifecycleOwner) { show ->
            if (show && errorSnackbar == null) {
                errorSnackbar = Snackbar.make(
                    binding.root,
                    R.string.analysis_load_error_message,
                    Snackbar.LENGTH_INDEFINITE
                ).apply {
                    addCallback(object : Snackbar.Callback() {
                        override fun onDismissed(transientBottomBar: Snackbar, event: Int) {
                            errorSnackbar = null
                        }
                    })

                    setAction(BaseR.string.retry_action) {
                        viewModel.retryLoading()
                    }

                    isGestureInsetBottomIgnored = true
                    show()
                }
            } else if (!show) {
                errorSnackbar?.dismiss()
                errorSnackbar = null
            }
        }


        selectedMapTypeLiveData.observeNotNull(viewLifecycleOwner) {
            sharedViewModel.updateForceSkiMap(it)
            setMapType(it)
        }

        var isInitialMap3dEnabled = true
        map3dEnabledLiveData.observeNotNull(viewLifecycleOwner) { enabled ->
            mapView.getMapAsync { map ->
                map.setMap3dModeEnabled(enabled)
                if (!isInitialMap3dEnabled) {
                    viewModel.onMap3dModeChanged(enabled)
                }

                isInitialMap3dEnabled = false
            }
        }

        viewModel.lapMarkerListLiveData.observeNotNull(viewLifecycleOwner) {
            mapView.updateLapMarkers(it)
        }

        mapView.getMapAsync { map ->
            map.getUiSettings().setTiltTo3dEnabled(true)
            map.addOnMap3dModeChangedWithTiltListener(onMap3dModeChangedWithTiltListener)
        }

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.workoutMapAnalysisDataShareFlow
                    .filterNotNull()
                    .combine(mainGraphTypeGraphTypeChangeViewModel.mainGraphTypeStateFlow) { data, graphType ->
                        Pair(data, graphType)
                    }.collect { (data, graphType) ->
                        viewModel.loadColorfulTrackRouteData(graphType, data)
                    }
            }
        }
    }

    private fun setMapType(mapType: MapType) {
        binding.avalancheInfo.isVisible = mapType.isAvalancheMap
        mapView.changeMapType(mapType)
        avalancheInfoHelper.showAvalancheInfoIfNeeded(mapType, childFragmentManager)
    }

    private fun updateAspectRatio() {
        if (mapView.height > 0) {
            viewModel.aspectRatio = mapView.width / mapView.height.toDouble()
        }
    }

    private fun update2d(cameraConfig: Workout2DPlaybackCameraConfig) {
        mapView.getMapAsync { map ->
            mapView.update2dLocationMarker(cameraConfig.markerPosition)
            if (cameraConfig.cameraPosition != null) {
                // mapbox: update camera padding
                mapView.moveCamera(newLatLng(cameraConfig.cameraPosition))
                mapView.showCenteredLocationView(
                    map.getProjection().toScreenLocation(cameraConfig.cameraPosition)
                )
            } else {
                mapView.hideCenteredLocationView()
            }
        }
    }

    private fun update3d(cameraConfig: Workout3DPlaybackCameraConfig) {
        mapView.update3dLocationMarker(
            LatLng(cameraConfig.markerPosition.latitude, cameraConfig.markerPosition.longitude),
            cameraConfig.markerAltitude
        )

        updateFreeCam(cameraConfig)
    }

    private fun updateFreeCam(config: Workout3DPlaybackCameraConfig) {
        config.cameraPosition ?: return

        mapView.moveCamera(
            SuuntoFreeCameraUpdate(
                config.markerPosition,
                config.cameraPosition,
                config.cameraAltitude,
                config.cameraPitch,
                config.cameraBearing
            )
        )
    }

    private fun showMap(state: WorkoutMapRouteData) {
        with(mapView) {
            try {
                when (state) {
                    is BasicWorkoutMapRouteData -> {
                        showWorkout(
                            activityRoutes = state.routeWithDashLinePoints?.solidLineRoutePoints.orEmpty(),
                            nonActivityRoutes = state.routeWithDashLinePoints?.dashLineRoutePoints.orEmpty(),
                            highlightedLapPoints = state.highlightedLapLatLngs,
                            nonHighlightedPoints = state.nonHighlightedLatLngs,
                            startPoint = state.startPoint,
                            endPoint = state.endPoint,
                            bounds = state.bounds,
                            animate = false,
                            disableZoomToBounds = state.disableZoomToBounds,
                        )
                    }
                    is MultisportWorkoutMapRouteData -> {
                        showMultisportWorkout(
                            activityRoutes = state.routeWithDashLinePoints?.solidLineRoutePoints.orEmpty(),
                            nonActivityRoutes = state.routeWithDashLinePoints?.dashLineRoutePoints.orEmpty(),
                            highlightedLapPoints = state.highlightedLapLatLngs,
                            nonHighlightedPoints = state.nonHighlightedLatLngs,
                            startPoint = state.startPoint,
                            endPoint = state.endPoint,
                            bounds = state.bounds,
                            animate = false,
                            inactiveMultisportPartRoutes = state.inactiveMultisportPartRoutes,
                            activityTypeChangeLocations = state.activityTypeChangeLocations,
                            disableZoomToBounds = state.disableZoomToBounds,
                        )
                    }
                    is HuntingOrFishingMapRouteData -> {
                        showHuntingOrFishingWorkout(
                            activityRoutes = state.routeWithDashLinePoints?.solidLineRoutePoints.orEmpty(),
                            nonActivityRoutes = state.routeWithDashLinePoints?.dashLineRoutePoints.orEmpty(),
                            highlightedLapPoints = state.highlightedLapLatLngs,
                            nonHighlightedPoints = state.nonHighlightedLatLngs,
                            startPoint = state.startPoint,
                            endPoint = state.endPoint,
                            bounds = state.bounds,
                            animate = false,
                            traverseEvents = state.traverseEvents
                        )
                    }
                    is SkiWorkoutMapRouteData -> {
                        showSkiWorkout(
                            activityRoutes = state.routeWithDashLinePoints?.solidLineRoutePoints.orEmpty(),
                            nonActivityRoutes = state.routeWithDashLinePoints?.dashLineRoutePoints.orEmpty(),
                            startPoint = state.fullRoute.first(),
                            endPoint = state.fullRoute.last(),
                            bounds = state.bounds,
                            animate = false,
                            highlightedRuns = state.highlightedRuns,
                            nonHighlightedRuns = state.nonHighlightedRuns,
                            highlightedLifts = state.highlightedLifts,
                            nonHighlightedLifts = state.nonHighlightedLifts,
                            disableZoomToBounds = state.disableZoomToBounds,
                        )
                    }
                }
                showMapSuccessfullyCalled()
            } catch (t: Throwable) {
                Timber.w(t, "Showing workout map failed")
                binding.hideMapLoadingView.visibility = View.GONE
                viewModel.onMapReadyForPlayback()
            }
        }
    }

    private fun showMapSuccessfullyCalled() {
        if (!showMapSuccessfullyCalledForView) {
            showMapSuccessfullyCalledForView = true
            mapView.getMapAsync { map ->
                map.addOnCameraIdleListener(object : GoogleMap.OnCameraIdleListener {
                    override fun onCameraIdle() {
                        map.removeOnCameraIdleListener(this)
                        updateMapBottomPadding()
                        binding.hideMapLoadingView.visibility = View.GONE
                        viewModel.onMapReadyForPlayback()
                    }
                })
            }
        }
    }

    private fun updateMapBottomPadding() {
        val offset = if (binding.bottomSheetGraphAnalysis.isVisible) {
            if (binding.bottomSheetGraphAnalysis.top != 0) {
                binding.root.height - binding.bottomSheetGraphAnalysis.top
            } else {
                // Either the bottom sheet is actually at the top, or we've just rotated
                // the screen and the bottom sheet behavior hasn't adjusted the position.
                // Assume its the latter, and rely on STATE_EXPANDED case returning the correct
                // offset for the former case as well. This special handling is needed in case
                // [showMap] is called before the bottom sheet behavior moves the sheet the correct
                // location, having the map padding at the top of the map causes the map be
                // unnecessarily zoomed out
                val sheetBehavior = BottomSheetBehavior.from(binding.bottomSheetGraphAnalysis)
                when (sheetBehavior.state) {
                    BottomSheetBehavior.STATE_EXPANDED -> sheetBehavior.expandedOffset
                    BottomSheetBehavior.STATE_HALF_EXPANDED -> {
                        (binding.root.height * sheetBehavior.halfExpandedRatio).roundToInt()
                    }

                    else -> sheetBehavior.peekHeight
                }
            }
        } else 0
        if (offset != mapView.getBottomMapPadding()) {
            mapView.setBottomMapPadding(offset)
            mapView.resetLastCameraUpdate()
        }
    }

    override fun onGraphAnalysisChartReady(): Pair<Boolean, Boolean> {
        val sheetBehavior = BottomSheetBehavior.from(binding.bottomSheetGraphAnalysis)
        val result = if (binding.bottomSheetGraphAnalysis.isVisible
            && shouldAutoOpenBottomSheetOnGraphReady
            && arguments?.getBoolean(WorkoutMapGraphAnalysisViewModel.ARG_AUTO_PLAYBACK) == false
            && sheetBehavior.state != BottomSheetBehavior.STATE_EXPANDED
            && sheetBehavior.state != BottomSheetBehavior.STATE_HALF_EXPANDED
        ) {
            val autoOpenState = BottomSheetBehavior.STATE_HALF_EXPANDED

            sheetBehavior.addBottomSheetCallback(object :
                BottomSheetBehavior.BottomSheetCallback() {
                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    if (newState == autoOpenState) {
                        sheetBehavior.removeBottomSheetCallback(this)
                        viewModel.onChartReadyForPlayback()
                    }
                }

                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    // Do nothing
                }
            })
            viewModel.onBottomSheetAutomatedOpeningStarted(
                AnalyticsPropertyValue.WorkoutAnalysisBottomSheetOpenedContext.ENTERING_ANALYSIS_VIEW
            )
            sheetBehavior.state = autoOpenState
            Pair(true, true)
        } else {
            viewModel.onChartReadyForPlayback()
            Pair(false, false)
        }

        shouldAutoOpenBottomSheetOnGraphReady = false

        return result
    }

    override fun showFullscreenGraphAnalysis(initialMainGraphType: GraphType?) {
        sharedViewModel.viewState.value?.data?.workoutHeader?.data?.let {
            // navController can't be told to use launchActivityForResult, which means we can't
            // get results from an Activity destination to sync the highlight position to what was
            // selected in fullscreen mode when returning to this screen if we use navController to go there.
            // So instead of sending the event to our NavigationEventDispatcher that uses a navController,
            // launch the Activity here with the ActivityResultLauncher API.
            val graphAnalysisFragment =
                childFragmentManager.findFragmentById(R.id.graph_analysis_fragment_container) as? GraphAnalysisFragment
            val selections = graphAnalysisFragment?.getGraphAnalysisSelections()
                ?: GraphAnalysisSelections(0L, null)

            highlightMovedInFullscreenListener.launch(
                FullscreenGraphAnalysisActivity.newStartIntent(
                    context = requireContext(),
                    analyticsSource = MAP_ANALYSIS_OPEN_FULLSCREEN_BUTTON,
                    lockLandscape = true,
                    autoPlayback = false,
                    initialSelections = selections,
                    initialMainGraphType = initialMainGraphType,
                    workoutHeader = it,
                    multisportPartActivity = sharedViewModel.viewState.value?.data?.multisportPartActivity?.data
                )
            )
            viewModel.onFullscreenModeOpened()
        }
    }

    private fun updateBottomSheetBehaviorPeekHeight() {
        val decorView = requireActivity().window.decorView
        val rootWindowInsets = WindowInsetsCompat.toWindowInsetsCompat(decorView.rootWindowInsets)
        val navBarInset = rootWindowInsets.getInsets(WindowInsetsCompat.Type.systemBars()).bottom
        val bottomGestureInset =
            rootWindowInsets.getInsets(WindowInsetsCompat.Type.mandatorySystemGestures()).bottom
        val gestureInsetDifference = maxOf(bottomGestureInset - navBarInset, 0)

        val defaultPeekHeight =
            resources.getDimensionPixelSize(R.dimen.graph_analysis_bottom_sheet_peek_height)

        val infoContainer: View? = decorView.findViewById(R.id.graph_highlight_info_container)
        this.bottomSheetBehavior?.peekHeight = if (infoContainer != null) {
            // the calculation is tricky, if bottom sheet layout updated, it should be updated too.
            maxOf(
                binding.graphAnalysisFragmentContainer.top + infoContainer.top + infoContainer.height,
                defaultPeekHeight,
            )
        } else {
            defaultPeekHeight
        } + gestureInsetDifference
    }

    override fun closeFullscreenGraphAnalysis() {
        // No-op, we're already not in fullscreen
    }

    override fun onPlaybackClicked(): Boolean {
        if (viewModel.hasPremium.value) {
            viewModel.onTogglePlaybackClicked()
        } else {
            premiumPromotionNavigator.openWorkoutPlaybackPromotionDialog(
                childFragmentManager,
                AnalyticsPropertyValue.BuyPremiumPopupShownSource.WORKOUT_MAP_ANALYSIS
            )
        }
        return true
    }

    companion object {
        private const val ARG_INITIAL_MAIN_GRAPH_TYPE = "initialMainGraphType"
        private const val KEY_SHOULD_AUTO_OPEN_SHEET = "KEY_SHOULD_AUTO_OPEN_SHEET"
    }
}
