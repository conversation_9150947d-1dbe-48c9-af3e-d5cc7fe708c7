package com.stt.android.workout.details.competition

import com.stt.android.common.coroutines.ActivityRetainedCoroutineScope
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummaryData
import com.stt.android.utils.traceSuspend
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.WorkoutCompetitionNavEvent
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

interface CompetitionWorkoutDataLoader {
    val summaryStateFlow: StateFlow<ViewState<CompetitionWorkoutSummaryData?>>
    suspend fun loadCompetitionWorkout(workoutHeader: WorkoutHeader): StateFlow<ViewState<CompetitionWorkoutSummaryData?>>
}

@ActivityRetainedScoped
class DefaultCompetitionWorkoutDataLoader
@Inject constructor(
    private val workoutDataSource: WorkoutDataSource,
    private val currentUserController: CurrentUserController,
    private val navigationEventDispatcher: NavigationEventDispatcher,
    private val activityRetainedCoroutineScope: ActivityRetainedCoroutineScope
) : CompetitionWorkoutDataLoader {
    lateinit var workoutHeader: WorkoutHeader

    override val summaryStateFlow: MutableStateFlow<ViewState<CompetitionWorkoutSummaryData?>> =
        MutableStateFlow(loading())

    override suspend fun loadCompetitionWorkout(workoutHeader: WorkoutHeader): StateFlow<ViewState<CompetitionWorkoutSummaryData?>> {
        this.workoutHeader = workoutHeader
        val isOwnWorkout = workoutHeader.username == currentUserController.username

        if (!isOwnWorkout) {
            summaryStateFlow.value = loaded()
        } else {
            workoutHeader.key?.let {
                activityRetainedCoroutineScope.launch {
                    runSuspendCatching {
                        traceSuspend("loadCompetitionWorkout") {
                            summaryStateFlow.value = loaded(
                                CompetitionWorkoutSummaryData(
                                    workoutDataSource.fetchCompetitionWorkoutResult(workoutHeader.username, it),
                                    onCompetitionWorkoutClick = ::onCompetitionSummaryClick
                                )
                            )
                        }
                    }.onFailure {
                        Timber.w(it, "Loading competition workout summary failed.")
                        summaryStateFlow.value = loaded()
                    }
                }
            } ?: run { summaryStateFlow.value = loaded() }

        }
        return summaryStateFlow
    }


    private fun onCompetitionSummaryClick() {
        navigate(summaryStateFlow.value.data)
    }


    private fun navigate(competitionWorkoutSummary: CompetitionWorkoutSummaryData?) {
        if (competitionWorkoutSummary?.summary?.competition == null) return
        navigationEventDispatcher.dispatchEvent(
            WorkoutCompetitionNavEvent(
                workoutHeader,
                competitionWorkoutSummary.summary
            )
        )
    }
}
