package com.stt.android.home.dashboardv2.ui.widgets

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.theme.vo2MaxZone1
import com.stt.android.compose.theme.vo2MaxZone2
import com.stt.android.compose.theme.vo2MaxZone3
import com.stt.android.domain.diary.Vo2MaxRange
import com.stt.android.domain.diary.Vo2MaxState
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonChartWidget
import com.stt.android.home.dashboardv2.ui.widgets.common.vo2MaxMonthDayLabel
import com.stt.android.home.dashboardv2.widgets.Vo2MaxWidgetInfo
import com.stt.android.home.dashboardv2.widgets.labelResId
import com.stt.android.ui.utils.TextFormatter
import java.time.Year

@Composable
internal fun Vo2MaxWidget(
    widgetInfo: Vo2MaxWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val (subHeader, title, subtitle) = if (widgetInfo.latestVo2Max == null) {
        // Empty state: Display "--" for title and "No data" for subtitle as per Image 1
        Triple(
            stringResource(R.string.widget_no_data_title), // "--"
            stringResource(R.string.widget_no_data_title), // "--"
            stringResource(R.string.widget_no_data_subtitle), // "No data"
        )
    } else {
        // Data state: Display actual VO2 Max value, date, and status as per Image 2
        Triple(
            widgetInfo.latestVo2MaxDate?.let { latestVo2MaxDate ->
                val vo2MaxLocaleDate = latestVo2MaxDate.toLocalDate()
                if (vo2MaxLocaleDate.year == Year.now().value) {
                    vo2MaxLocaleDate.vo2MaxMonthDayLabel
                } else {
                    TextFormatter.formatDate(context, latestVo2MaxDate, true)
                }
            }.orEmpty(),
            TextFormatter.formatVo2Max(widgetInfo.latestVo2Max),
            widgetInfo.state?.let { stringResource(it.labelResId) }.orEmpty(),
        )
    }
    CommonChartWidget(
        editMode = editMode,
        headerRes = R.string.dashboard_widget_title_max_vo2,
        iconRes = R.drawable.ic_dashboard_widget_max_vo2,
        colorRes = R.color.dashboard_widget_max_vo2,
        subheaderText = subHeader,
        titleText = AnnotatedString(title),
        subtitleText = subtitle,
        onClick = onClick,
        onLongClick = onLongClick,
        onRemoveClick = onRemoveClick,
        modifier = modifier,
    ) {
        Vo2MaxProgress(widgetInfo = widgetInfo)
    }
}

@Composable
internal fun Vo2MaxProgress(
    widgetInfo: Vo2MaxWidgetInfo,
    modifier: Modifier = Modifier,
) {
    val currentVo2 = widgetInfo.latestVo2Max

    // Both empty state and data state use the same rangeItemList from DashboardVo2MaxUseCase
    // The difference is in adjustByValue processing:
    // - Empty state (vo2Max = null): returns original ranges based on user's age/gender
    // - Data state (vo2Max != null): adjusts ranges to include current VO2 Max value
    val displayRanges = widgetInfo.rangeItemList
    val displayMinValue = displayRanges.minOfOrNull { it.minValue } ?: 33
    val displayMaxValue = displayRanges.maxOfOrNull { it.maxValue } ?: 56

    // Use the calculated min/max values for proper scaling
    val minValue = displayMinValue
    val maxValue = displayMaxValue

    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Vo2MaxProgressBar(
            currentVo2 = currentVo2,
            vo2MaxState = widgetInfo.state,
            minValue = minValue,
            maxValue = maxValue,
            rangeItemList = displayRanges,
        )

        Spacer(
            modifier = Modifier
                .height(MaterialTheme.spacing.xsmall)
                .fillMaxWidth()
        )

        Vo2MaxMarker(
            currentVo2 = currentVo2,
            minValue = minValue,
            maxValue = maxValue,
            rangeItemList = displayRanges,
        )
    }
}

@Composable
private fun Vo2MaxProgressBar(
    currentVo2: Float?,
    vo2MaxState: Vo2MaxState?,
    minValue: Int,
    maxValue: Int,
    rangeItemList: List<Vo2MaxRange>,
    modifier: Modifier = Modifier,
) {
    val rangeItems = remember(currentVo2) {
        rangeItemList
    }
    var indicatorOffsetX by remember { mutableFloatStateOf(0f) }

    val zone1NormalColor = MaterialTheme.colorScheme.vo2MaxZone1
    val zone1EmptyColor = MaterialTheme.colorScheme.nearWhite
    val zone2NormalColor = MaterialTheme.colorScheme.vo2MaxZone2
    val zone2EmptyColor = MaterialTheme.colorScheme.lightGrey
    val zone3NormalColor = MaterialTheme.colorScheme.vo2MaxZone3
    val zone3EmptyColor = MaterialTheme.colorScheme.cloudyGrey

    Box(
        modifier = modifier.fillMaxWidth()
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(12.dp)
                .clip(RoundedCornerShape(2.dp))
                .align(Alignment.Center)
        ) {
            val canvasWidth = size.width
            val canvasHeight = size.height
            var rangeOffsetX = 0f

            rangeItems.forEachIndexed { index, rangeItem ->
                // Use different colors for empty state vs data state
                val progressColor = if (currentVo2 == null) {
                    // Empty state: Use muted colors
                    when (rangeItem.state) {
                        Vo2MaxState.POOR,
                        Vo2MaxState.VERY_POOR -> zone1EmptyColor

                        Vo2MaxState.FAIR,
                        Vo2MaxState.GOOD -> zone2EmptyColor

                        Vo2MaxState.EXCELLENT,
                        Vo2MaxState.SUPERIOR -> zone3EmptyColor
                    }
                } else {
                    // Data state: Use normal colors based on adjusted ranges
                    when (rangeItem.state) {
                        Vo2MaxState.POOR,
                        Vo2MaxState.VERY_POOR -> zone1NormalColor

                        Vo2MaxState.FAIR,
                        Vo2MaxState.GOOD -> zone2NormalColor

                        Vo2MaxState.EXCELLENT,
                        Vo2MaxState.SUPERIOR -> zone3NormalColor
                    }
                }

                // Calculate range width based on the actual value range
                val rangeValueSpan = rangeItem.maxValue - rangeItem.minValue
                val totalValueSpan = maxValue - minValue
                val rangeWidth = canvasWidth * rangeValueSpan.toFloat() / totalValueSpan.toFloat()

                drawRect(
                    color = progressColor,
                    topLeft = Offset(rangeOffsetX, 0f),
                    size = Size(rangeWidth, canvasHeight),
                )
                rangeOffsetX += rangeWidth
            }
            if (currentVo2 != null) {
                indicatorOffsetX = canvasWidth * (currentVo2 - minValue) / (maxValue - minValue)
            }
        }

        // Only show indicator cursor when there's actual data (Image 2), not in empty state (Image 1)
        if (currentVo2 != null && vo2MaxState != null) {
            IndicatorCursor(
                vo2MaxState = vo2MaxState,
                modifier = Modifier.offset {
                    IntOffset(x = indicatorOffsetX.toInt(), y = 0)
                }
            )
        }
    }
}

@Composable
private fun Vo2MaxMarker(
    currentVo2: Float?,
    minValue: Int,
    maxValue: Int,
    rangeItemList: List<Vo2MaxRange>,
    modifier: Modifier = Modifier,
) {
    val textMeasurer = rememberTextMeasurer()
    val gray = MaterialTheme.colorScheme.mediumGrey
    val textStyle = MaterialTheme.typography.bodySmall.merge(gray)
    val markerLineHeight = LocalDensity.current.run { 3.dp.toPx() }
    val markerLabelSpace = LocalDensity.current.run { 8.dp.toPx() }

    Box(modifier = modifier.fillMaxWidth()) {
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(24.dp)
        ) {
            val canvasWidth = size.width
            val totalValueSpan = maxValue - minValue

            // Create marker positions based on the range boundaries
            val markerValues = mutableSetOf<Int>()

            // Both empty state and data state use the same rule:
            // Show maxValue from first Vo2MaxRange to second-to-last Vo2MaxRange
            // The rangeItemList comes from DashboardVo2MaxUseCase based on user's actual data
            val sortedRanges = rangeItemList.sortedBy { it.minValue }

            // Add maxValue of each range except the last one
            sortedRanges.forEachIndexed { index, range ->
                // Include all ranges except the last one (index < size - 1)
                if (index < sortedRanges.size - 1) {
                    markerValues.add(range.maxValue)
                }
            }

            // Sort and draw markers
            markerValues.sorted().forEach { value ->
                // Only draw markers within the valid range
                if (value in minValue..maxValue) {
                    val position = (value - minValue).toFloat() / totalValueSpan.toFloat()
                    val xOffset = canvasWidth * position

                    val text = value.toString()
                    val textLayoutResult = textMeasurer.measure(text)
                    val textSize = textLayoutResult.size

                    // Calculate label position to avoid overflow
                    val labelX = when {
                        xOffset <= textSize.width / 2f -> 0f // Left edge
                        xOffset >= canvasWidth - textSize.width / 2f -> canvasWidth - textSize.width // Right edge
                        else -> xOffset - textSize.width / 2f // Center
                    }

                    drawLine(
                        color = gray,
                        start = Offset(xOffset, 0f),
                        end = Offset(xOffset, markerLineHeight),
                        strokeWidth = density,
                    )
                    drawText(
                        textMeasurer = textMeasurer,
                        style = textStyle,
                        text = text,
                        topLeft = Offset(labelX, markerLineHeight + markerLabelSpace),
                    )
                }
            }
        }
    }
}

@Composable
internal fun IndicatorCursor(
    vo2MaxState: Vo2MaxState?,
    modifier: Modifier = Modifier
) {
    val cursorBgColor = when (vo2MaxState) {
        Vo2MaxState.POOR,
        Vo2MaxState.VERY_POOR -> MaterialTheme.colorScheme.vo2MaxZone1

        Vo2MaxState.GOOD,
        Vo2MaxState.FAIR -> MaterialTheme.colorScheme.vo2MaxZone2

        Vo2MaxState.SUPERIOR,
        Vo2MaxState.EXCELLENT -> MaterialTheme.colorScheme.vo2MaxZone3

        else -> MaterialTheme.colorScheme.primaryContainer
    }
    Box(
        modifier = modifier
            .size(5.dp, 18.dp)
            .border(
                width = MaterialTheme.spacing.xxxsmall,
                color = MaterialTheme.colorScheme.onPrimaryContainer,
                shape = RoundedCornerShape(2.dp),
            )
            .background(
                color = cursorBgColor,
                shape = RoundedCornerShape(2.dp),
            )
    )
}

@Preview(widthDp = 170, heightDp = 170)
@Preview(widthDp = 170, heightDp = 170, locale = "zh")
@Composable
private fun Vo2MaxWidgetPreview() {
    M3AppTheme {
        Vo2MaxWidget(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth(),
            widgetInfo = Vo2MaxWidgetInfo(
                latestVo2Max = 36.3f, // Match Image 2
                latestVo2MaxDate = 1736245494239L,
                state = Vo2MaxState.POOR, // Match Image 2 showing "Poor"
                rangeItemList = listOf(
                    // These ranges simulate the output of GetVo2MaxStateRangesUseCase.adjustByValue()
                    // which adjusts the ranges to include the current VO2 Max value
                    Vo2MaxRange(31, 39, Vo2MaxState.VERY_POOR, true),  // Adjusted min to include 36.3
                    Vo2MaxRange(39, 44, Vo2MaxState.POOR, true),
                    Vo2MaxRange(44, 51, Vo2MaxState.FAIR, true),
                    Vo2MaxRange(51, 56, Vo2MaxState.GOOD, true),
                    Vo2MaxRange(56, 62, Vo2MaxState.EXCELLENT, true),
                    Vo2MaxRange(62, 67, Vo2MaxState.SUPERIOR, true),   // Adjusted max
                )
            ),
            editMode = false,
            onClick = {},
            onLongClick = {},
            onRemoveClick = {}
        )
    }
}

@Preview(widthDp = 170, heightDp = 170)
@Preview(widthDp = 170, heightDp = 170, locale = "zh")
@Composable
private fun Vo2MaxWidgetEmptyPreview() {
    M3AppTheme {
        Vo2MaxWidget(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth(),
            widgetInfo = Vo2MaxWidgetInfo(
                latestVo2Max = null,
                latestVo2MaxDate = null,
                state = null, // No state when there's no data
                rangeItemList = listOf(
                    // This will be replaced by empty state ranges, but provided for consistency
                    // Empty state will show: 33-39 (red), 39-44 (yellow), 44-51 (yellow), 51-56 (green)
                    Vo2MaxRange(33, 39, Vo2MaxState.VERY_POOR, true),
                    Vo2MaxRange(39, 44, Vo2MaxState.POOR, true),
                    Vo2MaxRange(44, 51, Vo2MaxState.FAIR, true),
                    Vo2MaxRange(51, 56, Vo2MaxState.GOOD, true),
                    Vo2MaxRange(56, 62, Vo2MaxState.EXCELLENT, true),
                    Vo2MaxRange(62, 68, Vo2MaxState.SUPERIOR, true),
                )
            ),
            editMode = false,
            onClick = {},
            onLongClick = {},
            onRemoveClick = {}
        )
    }
}


