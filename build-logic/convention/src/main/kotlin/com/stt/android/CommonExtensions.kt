package com.stt.android

import com.android.build.api.dsl.CommonExtension
import org.gradle.api.JavaVersion
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.exclude
import org.gradle.kotlin.dsl.provideDelegate
import org.gradle.kotlin.dsl.withType
import org.jetbrains.kotlin.allopen.gradle.AllOpenExtension
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.plugin.KaptExtension
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

object Versions {
    val jvm = JavaVersion.VERSION_17
    const val compileSdk = 35
    const val minSdk = 26
    const val targetSdk = 34
}

internal fun Project.configureKotlinAndroid(
    commonExtension: CommonExtension<*, *, *, *, *, *>,
) {
    commonExtension.apply {
        compileSdk = Versions.compileSdk

        defaultConfig {
            minSdk = Versions.minSdk
            testApplicationId = "com.stt.android.test"
            testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
            vectorDrawables.useSupportLibrary = true

            // these are here to avoid breaking manifest merging in all variants
            manifestPlaceholders["XG_ACCESS_ID"] = ""
            manifestPlaceholders["XG_ACCESS_KEY"] = ""
            manifestPlaceholders["HW_APPID"] = ""
        }

        buildFeatures {
            viewBinding = true
        }

        compileOptions {
            sourceCompatibility = Versions.jvm
            targetCompatibility = Versions.jvm
            isCoreLibraryDesugaringEnabled = true
        }

        testOptions {
            unitTests {
                isIncludeAndroidResources = true
            }
            unitTests.all { test ->
                test.testLogging {
                    // show standard out and standard error of the test JVM(s) on the console
                    showStandardStreams = true
                    // Show that tests are run in the command-line output
                    events("passed")
                }
            }
        }

        packaging {
            jniLibs {
                pickFirsts += "lib/**/*.so"
            }
            resources {
                excludes += setOf(
                    "LICENSE.txt",
                    "META-INF/LICENSE",
                    "META-INF/LICENSE.txt",
                    "META-INF/LICENSE.md",
                    "META-INF/NOTICE",
                    "META-INF/NOTICE.md",
                    "META-INF/licenses/**",
                    // these added due to https://github.com/Kotlin/kotlinx.coroutines/issues/2023
                    "**/attach_hotspot_windows.dll",
                    "META-INF/AL2.0",
                    "META-INF/LGPL2.1",
                )
            }
        }
    }

    configureKotlin()

    dependencies {
        add("coreLibraryDesugaring", libs("android.desugar.nio"))
    }
}

/**
 * these are common dependencies that should be applied to all Android modules
 */
internal fun Project.configureCommonAndroidDependencies() {
    dependencies {
        // STTAndroidWear should have only minimal static dependencies (we don't want unexpected changes in that module)
        // STTAndroidCore follows as its a depencency to STTAndroidWear
        if (name == "STTAndroidWear" || name == "STTAndroidCore") return@dependencies

        "compileOnly"(libs("javax.inject"))

        add("implementation", platform(libs("firebase-bom")))
        if (name != "utils") "implementation"(project(":utils"))
        "implementation"(libs("kotlin.stdlib"))
        "implementation"(libs("kotlin.reflect"))
        "implementation"(libs("kotlin.coroutines"))
        "implementation"(libs("kotlin.coroutines.android"))
        "implementation"(libs("kotlin.coroutines.rxjava"))
        "implementation"(libs("kotlin.coroutines.play.services"))
        "implementation"(libs("timber"))

        if (name != "testutils") "testImplementation"(project(":testutils"))
        "testImplementation"(libs("mockito5"))
        "testImplementation"(libs("mockito5-kotlin"))
        "testImplementation"(libs("mockk"))
        "testImplementation"(libs("kotlin.coroutines.test"))
        "testImplementation"(libs("kotlin.test"))
        "testImplementation"(libs("assertj")) // todo remove and replace with truth
        "testImplementation"(libs("json.unit"))
        "testImplementation"(libs("truth"))
        "testImplementation"(libs("mockwebserver"))
        "testImplementation"(libs("turbine"))
        "testImplementation"(libs("androidx.arch.core.testing"))

        if (name != "testutils") "androidTestImplementation"(project(":testutils"))
        "androidTestImplementation"(libs("dexmaker")) {
            exclude("net.bytebuddy")
        }
        "androidTestImplementation"(libs("mockito5"))
        "androidTestImplementation"(libs("mockito5.kotlin"))
        "androidTestImplementation"(libs("androidx.test.core"))
        "androidTestImplementation"(libs("androidx.test.runner"))
        "androidTestImplementation"(libs("androidx.test.rules"))
        "androidTestImplementation"(libs("androidx.test.ext"))
        "androidTestImplementation"(libs("androidx.test.ext.ktx"))
        "androidTestImplementation"(libs("androidx.test.ext.truth"))
        "androidTestImplementation"(libs("androidx.arch.core.testing"))
        "androidTestImplementation"(libs("androidx.work.test"))
        "androidTestImplementation"(libs("kotlin.coroutines.test"))
        "androidTestImplementation"(libs("turbine"))
    }
}

internal fun Project.configureCommonKapt() {
    extensions.configure<KaptExtension> {
        javacOptions {
            // Increase the max count of errors from annotation processors.
            // Default is 100.
            // This fixes an issue where Dagger errors are not shown due to data binding,
            // generating too many errors when KAPT fails to build.
            option("-Xmaxerrs", 500.toString())
        }
    }
}

/**
 * Configure base Kotlin options
 */
private fun Project.configureKotlin() {
    // Use withType to workaround https://youtrack.jetbrains.com/issue/KT-55947
    tasks.withType<KotlinCompile>().configureEach {
        compilerOptions {
            // Set JVM target
            jvmTarget.set(JvmTarget.fromTarget(Versions.jvm.toString()))
            // Treat all Kotlin warnings as errors (disabled by default)
            // Override by setting warningsAsErrors=true in your ~/.gradle/gradle.properties
            val warningsAsErrors: String? by project
            allWarningsAsErrors.set(warningsAsErrors.toBoolean())
            freeCompilerArgs.add("-opt-in=kotlin.ExperimentalStdlibApi")
        }
    }

    // allopen plugin annotation is defined in utils module
    extensions.configure<AllOpenExtension> {
        annotation("com.stt.android.TestOpen")
    }
}
