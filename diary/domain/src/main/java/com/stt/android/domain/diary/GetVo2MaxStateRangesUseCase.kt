package com.stt.android.domain.diary

import com.stt.android.domain.CoroutineUseCase
import javax.inject.Inject
import kotlin.math.ceil
import kotlin.math.floor

class GetVo2MaxStateRangesUseCase @Inject constructor() :
    CoroutineUseCase<Pair<Vo2MaxState?, List<Vo2MaxRange>>, GetVo2MaxStateRangesUseCase.Params> {

    data class Params(
        val vo2Max: Float?,
        val isMale: <PERSON><PERSON><PERSON>,
        val userAge: Int,
    )

    override suspend fun run(params: Params): Pair<Vo2MaxState?, List<Vo2MaxRange>> {
        val isMale = params.isMale
        val vo2MaxRanges = when (params.userAge) {
            in Int.MIN_VALUE..29 -> LESS_THAN_30_VO2MAX_RANGES.filter { it.isMale == isMale }
            in 30..39 -> BETWEEN_30_AND_39_VO2MAX_RANGES.filter { it.isMale == isMale }
            in 40..49 -> BETWEEN_40_AND_49_VO2MAX_RANGES.filter { it.isMale == isMale }
            in 50..59 -> BETWEEN_50_AND_59_VO2MAX_RANGES.filter { it.isMale == isMale }
            in 60..69 -> BETWEEN_60_AND_69_VO2MAX_RANGES.filter { it.isMale == isMale }
            else -> GREATER_THAN_70_VO2MAX_RANGES.filter { it.isMale == isMale }
        }
        val vo2MaxState = params.vo2Max?.let { vo2Max ->
            vo2MaxRanges.find { it.contains(vo2Max) }?.state
        }
        return vo2MaxState to vo2MaxRanges.adjustByValue(params.vo2Max)
    }

    private fun List<Vo2MaxRange>.adjustByValue(vo2Max: Float?): List<Vo2MaxRange> {
        if (vo2Max == null) return this
        var minValue = first { it.state == Vo2MaxState.POOR }.minValue - VO2MAX_RANGE_OFFSET
        var maxValue = first { it.state == Vo2MaxState.EXCELLENT }.maxValue + VO2MAX_RANGE_OFFSET
        if (vo2Max < minValue) minValue = vo2Max
        if (vo2Max > maxValue) maxValue = vo2Max
        return map { range ->
            when (range.state) {
                Vo2MaxState.VERY_POOR -> range.copy(minValue = floor(minValue).toInt())
                Vo2MaxState.POOR,
                Vo2MaxState.FAIR,
                Vo2MaxState.GOOD,
                Vo2MaxState.EXCELLENT -> range

                Vo2MaxState.SUPERIOR -> range.copy(maxValue = ceil(maxValue).toInt())
            }
        }
    }

    companion object {
        private const val VO2MAX_RANGE_OFFSET = 5f
    }
}
